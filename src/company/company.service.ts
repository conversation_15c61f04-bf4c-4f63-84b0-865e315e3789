import { Connection, Model } from "mongoose";
import { ConfigService } from "@nestjs/config";
import { InjectConnection, InjectModel } from "@nestjs/mongoose";
import { MailService } from "src/mail/mail.service";
import CreatedResponse from "src/shared/http/response/created.http";
import OkResponse from "src/shared/http/response/ok.http";
import { UserDocument } from "src/user/schema/user.schema";
import { UserService } from "src/user/user.service";
import { CreateCompanyDto } from "./dto/create-company.dto";
import { CreateMemberDto } from "./dto/create-member.dto";
import { InvitationResponseDto } from "./dto/invitation-response.dto";
import { InviteMemberDto } from "./dto/invite-member.dto";
import { CompanyDocument } from "./schema/company.schema";
import { InvitationDocument } from "./schema/invitation.schema";
import { randomUUID } from "crypto";
import { UserRolesEnum } from "./enum/role.enum";
import { MemberDocument } from "./schema/member.schema";
import { PaginationRequestDto } from "./dto/pagination-request.dto";
import { UserInvitationDto } from "./dto/user-invitation.dto";
import { PositionService } from "src/position/position.service";
import { InvitationStatusEnum } from "./enum/invitation-status.enum";
import { DepartmentService } from "src/department/department.service";
import { UpdateMemberCompanyInfoDto } from "./dto/update-member-company-info.dto";
import { GetCompanyMemberDto, FetchMemberDto } from "./dto/get-company-member.dto";
import { CompensationService } from "src/compensation/compensation.service";
import { RoleService } from "src/role/role.service";
import { PermissionsEnum } from "src/shared/enum/permission.enum";
import { PieceWorkService } from "src/piece-work/piece-work.service";
import { CrewDocument } from "src/crew/schema/crew-management.schema";
import { CrewMemberDocument } from "src/crew/schema/crew-member.schema";
import {
    BadRequestException,
    ConflictException,
    HttpException,
    HttpStatus,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
} from "@nestjs/common";
import NoContentResponse from "src/shared/http/response/no-content.http";
import { CompanySettingDocument } from "./schema/company-setting.schema";
import { CreateCompanySettingDto } from "./dto/create-company-setting.dto";
import { defaultCompanyCommision, defaultCompanySetting, defaultMediaSetting } from "src/shared/constants";
import { WorkTaskService } from "src/work-task/work-task.service";
import { CityService } from "src/city/city.service";
import { CrmService } from "src/crm/crm.service";
import { ChannelService } from "src/marketing-setting/channel.service";
import { LeadSourceService } from "src/marketing-setting/lead-source.service";
import { ProjectService } from "src/project/project.service";
import { PayScheduleService } from "src/pay-schedule/pay-schedule.service";
import { CompanyPayDocument } from "./schema/company-pay.schema";
import { CreateCompanyCommisionDto } from "./dto/create-company-commision.dto";
import { TerminateMemberDto } from "./dto/terminate-member.dto";
import { getInitials, roundTo3 } from "src/shared/helpers/logics";
import { ReferrersDocument } from "./schema/referrers.schema";
import { CreateReferrerDto } from "./dto/create-referrer.dto";
import { DeleteRestoreReferrerDto } from "./dto/delete-referrer.dto";
import { StripeService } from "src/stripe/stripe.service";
import { SubscriptionPlanDocument } from "src/subscription/schema/subscription.schema";
import {
    SubscriptionPlanTypeEnum,
    SubscriptionRenewalPeriod,
    SubscriptionStatusEnum,
} from "src/shared/enum/subscriptions.enum";
import {
    SUBSCRIPTION_PRICE_COLLECTION_NAME,
    SubscriptionPriceDocument,
} from "src/subscription/schema/price.schema";
import { CompanyAnalyticsService } from "./company-analytics.service";
import { UpdateCompanyDto } from "./dto/update-company.dto";
import { WageIntervalEnum } from "src/compensation/enum/wage-interval.enum";
import { ContractDocument } from "./schema/contract-schema";
import { CreateContractDto, CreateSectionDto } from "./dto/create-contract.dto";
import { UpdateContractDto, UpdateSectionDto, UpdateSectionOrderDto } from "./dto/update-contract.dto";
import { InvitationWageDto } from "./dto/invitation-wage-update.dto";
import { ClientDocument } from "src/client/schema/client.schema";
import { CompensationDocument } from "src/compensation/schema/compensation.schema";
import { CrmCheckpointDocument } from "src/crm/schema/crm-checkpoint.schema";
import { CrmStageDocument } from "src/crm/schema/crm-stage.schema";
import { CrmStepDocument } from "src/crm/schema/crm-step.schema";
import { ActivityLogDocument } from "src/activity-log/schema/activity-log.schema";
import { OpportunityDocument } from "src/opportunity/schema/opportunity.schema";
import { DailyLogDocument } from "src/daily-log/schema/daily-log.schema";
import { PayScheduleDocument } from "src/pay-schedule/schema/pay-schedule.schema";
import { PieceWorkSettingDocument } from "src/piece-work/schema/piece-work-setting.schema";
import { PieceWorkDocument } from "src/piece-work/schema/piece-work.schema";
import { CategoryDocument } from "src/project/schema/category.schema";
import { CrewPositionDocument } from "src/project/schema/crew-position.schema";
import { InputDocument } from "src/project/schema/input.schema";
import { MaterialDocument } from "src/project/schema/material.schema";
import { OptionsDocument } from "src/project/schema/options.schema";
import { OrderDocument } from "src/project/schema/order.schema";
import { PackageDocument } from "src/project/schema/package.schema";
import { PriceDocument } from "src/project/schema/price-schema";
import { ProjectTypeDocument } from "src/project/schema/project-type.schema";
import { ProjectDocument } from "src/project/schema/project.schema";
import { SubCategoryDocument } from "src/project/schema/sub-category.schema";
import { TaskDocument } from "src/project/schema/task.schema";
import { TaxJurisdictionDocument } from "src/project/schema/tax-jurisdiction.schema";
import { UnitDocument } from "src/project/schema/unit.schema";
import { SubcontractorDocument } from "src/subcontractor/schema/subcontractor.schema";
import { TimeCardDocument } from "src/time-card/schema/time-card.schema";
import { WorkTaskDocument } from "src/work-task/schema/work-task.schema";
import { CityDocument } from "src/city/schema/city.schema";
import { CommissionModificationDocument } from "src/opportunity/schema/opp-commission.schema";
import { CompanyAnalyticsDocument } from "./schema/company-analytics.schema";
import { LeadDocument } from "src/lead/schema/lead.schema";
import { CustomProjectDocument } from "src/custom-project/schema/custom-project.schema";
import { DepartmentDocument } from "src/department/schema/department.schema";
import { GpsDocument } from "src/gps/schema/gps.schema";
import { LeadSourceDocument } from "src/marketing-setting/schema/lead-source.schema";
import { MarketingChannelDocument } from "src/marketing-setting/schema/channel.schema.dto";
import { PayRollDocument } from "src/payroll/schema/payroll.schema";
import { PositionDocument } from "src/position/schema/position.schema";
import { RoleDocument } from "src/role/schema/role.schema";
import { SalesActionDocument } from "src/crm/schema/sales-action.schema";
import { MediaSettingDocument } from "./schema/media-setting.schema";
import { CreateMediaSettingDto } from "./dto/create-media-setting.dto";
import { UpdateTagDto } from "./dto/update-company-tag.dto";
import { DeleteTagDto } from "./dto/delete-company-tag.dto";
import { CreateSalesActionCompanyDto } from "./dto/create-company-sales-action.dto";
import { UpdateSalesActionDto } from "src/crm/dto/update-sales-action.dto";
import { DeleteSalesActionDto } from "src/crm/dto/delete-sales-action.dto";
import { ContentBlockDocument } from "./schema/content-block.schema";
import { CreateContentBlockDto } from "./dto/create-content.dto";
import { GetContentDto } from "./dto/get-content.dto";
import { UpdateContentBlockDto } from "./dto/update-content.dto";
import { ContentBlockTypeEnum } from "./enum/content-type.enum";
import { StageGroupEnum } from "src/crm/enum/stage-group.enum";
import { OpportunityStatusEnum } from "src/opportunity/enum/opportunityStatus.enum";
import { ContactsService } from "src/contacts/contacts.service";

@Injectable()
export class CompanyService {
    constructor(
        private readonly crmService: CrmService,
        private readonly roleService: RoleService,
        private readonly mailService: MailService,
        private readonly userService: UserService,
        private readonly cityService: CityService,
        private readonly configService: ConfigService,
        private readonly projectService: ProjectService,
        private readonly positionService: PositionService,
        private readonly workTaskService: WorkTaskService,
        private readonly pieceWorkService: PieceWorkService,
        private readonly departmentService: DepartmentService,
        private readonly leadSourceService: LeadSourceService,
        private readonly compensationService: CompensationService,
        private readonly marketingChannelService: ChannelService,
        private readonly payScheduleService: PayScheduleService,
        private readonly stripeService: StripeService,
        private readonly companyAnalyticsService: CompanyAnalyticsService,
        private readonly contactService: ContactsService,
        @InjectConnection() private readonly connection: Connection,
        @InjectModel("User") private readonly userModel: Model<UserDocument>,
        @InjectModel("Crew") private readonly crewModel: Model<CrewDocument>,
        @InjectModel("Member") private readonly memberModel: Model<MemberDocument>,
        @InjectModel("Company") private readonly companyModel: Model<CompanyDocument>,
        @InjectModel("CrewMember") private readonly crewMemberModel: Model<CrewMemberDocument>,
        @InjectModel("Invitation") private readonly invitationModel: Model<InvitationDocument>,
        @InjectModel("CompanySetting") private readonly companySettingModel: Model<CompanySettingDocument>,
        @InjectModel("MediaSetting") private readonly mediaSettingModel: Model<MediaSettingDocument>,
        @InjectModel("CompanyPay")
        private readonly companyPayModel: Model<CompanyPayDocument>,
        @InjectModel("Referrers")
        private readonly referrersModel: Model<ReferrersDocument>,
        @InjectModel("SubscriptionPlans")
        private readonly subscriptionPlanModel: Model<SubscriptionPlanDocument>,
        @InjectModel("Contracts")
        private readonly contractModel: Model<ContractDocument>,
        @InjectModel(SUBSCRIPTION_PRICE_COLLECTION_NAME)
        private readonly subscriptionPriceModel: Model<SubscriptionPriceDocument>,
        @InjectModel("ProjectType") private readonly projectTypeModel: Model<ProjectTypeDocument>,
        @InjectModel("Order") private readonly orderModel: Model<OrderDocument>,
        @InjectModel("Compensation") private readonly compensationModel: Model<CompensationDocument>,
        @InjectModel("PaySchedule") private readonly payScheduleModel: Model<PayScheduleDocument>,
        @InjectModel("Client") private readonly clientModel: Model<ClientDocument>,
        @InjectModel("Opportunity") private readonly opportunityModel: Model<OpportunityDocument>,
        @InjectModel("CrmStage") private readonly crmStageModel: Model<CrmStageDocument>,
        @InjectModel("CrmStep") private readonly crmStepModel: Model<CrmStepDocument>,
        @InjectModel("ActivityLog")
        private readonly activityModel: Model<ActivityLogDocument>,
        @InjectModel("Input") private readonly inputModel: Model<InputDocument>,
        @InjectModel("Unit") private readonly unitModel: Model<UnitDocument>,
        @InjectModel("Project") private readonly projectModel: Model<ProjectDocument>,
        @InjectModel("Price") private readonly priceModel: Model<PriceDocument>,
        @InjectModel("Task") private readonly taskModel: Model<TaskDocument>,
        @InjectModel("Package") private readonly packageModel: Model<PackageDocument>,
        @InjectModel("Options") private readonly optionsModel: Model<OptionsDocument>,
        @InjectModel("Material") private readonly materialModel: Model<MaterialDocument>,
        @InjectModel("Category") private readonly categoryModel: Model<CategoryDocument>,
        @InjectModel("SubCategory") private readonly subCategoryModel: Model<SubCategoryDocument>,
        @InjectModel("CrewPosition") private readonly crewPositionModel: Model<CrewPositionDocument>,
        @InjectModel("TimeCard") private readonly timeCardModel: Model<TimeCardDocument>,
        @InjectModel("PieceWork")
        private readonly pieceWorkModel: Model<PieceWorkDocument>,
        @InjectModel("DailyLog") private readonly dailyLogModel: Model<DailyLogDocument>,
        @InjectModel("TaxJurisdiction") private readonly taxModel: Model<TaxJurisdictionDocument>,
        @InjectModel("PieceWorkSetting")
        private readonly pieceWorkSettingModel: Model<PieceWorkSettingDocument>,
        @InjectModel("City") private readonly cityModel: Model<CityDocument>,
        @InjectModel("CommissionModification")
        private readonly commissionModificationModel: Model<CommissionModificationDocument>,
        @InjectModel("CompanyAnalytics")
        private readonly companyAnalyticsModel: Model<CompanyAnalyticsDocument>,
        @InjectModel("Contracts") private readonly contractsModel: Model<ContractDocument>,
        @InjectModel("CrmCheckpoint") private readonly crmCheckpointModel: Model<CrmCheckpointDocument>,
        @InjectModel("Lead") private readonly leadModel: Model<LeadDocument>,
        @InjectModel("CustomProject") private readonly customProjectModel: Model<CustomProjectDocument>,
        @InjectModel("Departments") private readonly departmentsModel: Model<DepartmentDocument>,
        @InjectModel("Gps") private readonly gpsModel: Model<GpsDocument>,
        @InjectModel("Invitation") private readonly invitationsModel: Model<InvitationDocument>,
        @InjectModel("LeadSource") private readonly leadSourcesModel: Model<LeadSourceDocument>,
        @InjectModel("Marketing") private readonly marketingModel: Model<MarketingChannelDocument>,
        @InjectModel("Member") private readonly membersModel: Model<MemberDocument>,
        @InjectModel("Payroll") private readonly payrollModel: Model<PayRollDocument>,
        @InjectModel("Position") private readonly positionModel: Model<PositionDocument>,
        @InjectModel("Role") private readonly roleModel: Model<RoleDocument>,
        @InjectModel("SalesAction") private readonly salesActionModel: Model<SalesActionDocument>,
        @InjectModel("Subcontractor") private readonly subContractorModel: Model<SubcontractorDocument>,
        @InjectModel("WorkTask") private readonly worktasksModel: Model<WorkTaskDocument>,
        @InjectModel("ContentBlock") private readonly contentBlockModel: Model<ContentBlockDocument>,
    ) {}

    async addCompanyDefaultData(
        userId: string,
        companyId: string,
        companyName: string,
        plan: SubscriptionPlanTypeEnum,
        memberId: string,
    ) {
        try {
            await this.cityService.addDefaultCity(companyId, memberId);
            await this.departmentService.addDefaultDepartments(companyId, memberId);

            //Generating UUID for diff fields

            const unitUUID = {
                LB: randomUUID(),
                BDL: randomUUID(),
                BX: randomUUID(),
                LF: randomUUID(),
                QT: randomUUID(),
                EA: randomUUID(),
                RL: randomUUID(),
                SQ: randomUUID(),
                SF: randomUUID(),
                SHT: randomUUID(),
                GL: randomUUID(),
                BKT: randomUUID(),
                TN: randomUUID(),
                CTN: randomUUID(),
                BAG: randomUUID(),
                SG: randomUUID(),
                FF: randomUUID(),
            };

            const taskGroup = {
                "B-vents": randomUUID(),
                Chimney: randomUUID(),
                Flashing: randomUUID(),
                "Flat Roofing": randomUUID(),
                Other: randomUUID(),
                Pipes: randomUUID(),
                "Ridge Cap": randomUUID(),
                "Ridge Vent": randomUUID(),
                Sheeting: randomUUID(),
                Siding: randomUUID(),
                Skylight: randomUUID(),
                Starter: randomUUID(),
                "Steep Roofing": randomUUID(),
                "Tear Off": randomUUID(),
                Underlayments: randomUUID(),
                Ventilation: randomUUID(),
            };

            const crewPositionUUID = {
                roofer: randomUUID(),
                tearOff: randomUUID(),
                carpenter: randomUUID(),
                sider: randomUUID(),
            };

            const subCategoryIdUUID = {
                Miscellaneous: randomUUID(),
                Flashings: randomUUID(),
                Fasteners: randomUUID(),
                Ventilation: randomUUID(),
                Shingles: randomUUID(),
                TearOff: randomUUID(),
                Underlayments: randomUUID(),
                Flat: randomUUID(),
                Starter: randomUUID(),
                Ridge: randomUUID(),
                Skylights: randomUUID(),
                Trim: randomUUID(),
                Gutters: randomUUID(),
                Lumber: randomUUID(),
                Fascia: randomUUID(),
                Siding: randomUUID(),
            };

            const categoryIdUUID = {
                Roofing: randomUUID(),
                Siding: randomUUID(),
            };

            const projectTypeUUID = {
                replacement: randomUUID(),
                repair: randomUUID(),
            };

            const positionIdUUID = {
                Owner: randomUUID(),
                Admin: randomUUID(),
                GeneralManager: randomUUID(),
                PayrollAdmin: randomUUID(),
                OperationsAdmin: randomUUID(),
                ProjectManager: randomUUID(),
                Foreman: randomUUID(),
                CrewMember: randomUUID(),
                SalesManager: randomUUID(),
                SalesPerson: randomUUID(),
            };

            const versionIdUUID = {
                default: randomUUID(),
                salaried: randomUUID(),
            };
            const workTaskUUID = {
                "Tear Off": randomUUID(),
                Roofing: randomUUID(),
                Office: randomUUID(),
                Repairs: randomUUID(),
                Shop: randomUUID(),
                Meetings: randomUUID(),
            };

            //////////////
            // company type = free & pro (piece work)
            await Promise.all([
                this.positionService.addDefaultPositions(
                    userId,
                    companyId,
                    positionIdUUID,
                    plan === SubscriptionPlanTypeEnum.PROPLUS,
                ),
                this.projectService.addDefaultCategory(companyId, memberId, categoryIdUUID),
                this.projectService.addDefaultSubCategory(
                    companyId,
                    memberId,
                    subCategoryIdUUID,
                    categoryIdUUID,
                ),
                // this.projectService.addDefaultCrewPosition(companyId, memberId, crewPositionUUID),
                this.workTaskService.addDefaultWorkTask(
                    companyId,
                    memberId,
                    positionIdUUID,
                    workTaskUUID,
                    plan === SubscriptionPlanTypeEnum.PROPLUS,
                ),
                this.pieceWorkService.addDefaultPieceWorksSetting(
                    companyId,
                    memberId,
                    versionIdUUID,
                    unitUUID,
                    workTaskUUID,
                ),
                this.projectService.addUnitSetting(companyId, memberId, unitUUID),
                this.projectService.addDefaultProjectType(companyId, memberId, projectTypeUUID, taskGroup),
                this.payScheduleService.addDefaultPaySchedule(companyId, memberId),
                //default lead Source and Marketing channel
                this.marketingChannelService.addDefaultMarketingChannelsAndLeadSourceSetting(
                    companyId,
                    memberId,
                ),
                this.upsertCompanySetting(companyId, {
                    _id: randomUUID(),
                    createdBy: memberId,
                    defaultPO: getInitials(companyName),
                    ...defaultCompanySetting,
                }),
                this.upsertCompanyCommision(companyId, {
                    _id: randomUUID(),
                    createdBy: memberId,
                    ...defaultCompanyCommision(positionIdUUID, versionIdUUID),
                }),
            ]);

            // await this.leadSourceService.addDefaultLeadSourceSetting(companyId, memberId);

            const { data } = await this.payScheduleService.getPaySchedules(userId, companyId);
            const currDate = new Date(new Date().getTime() - 24 * 60 * 60 * 1000);
            const lastDay = new Date(currDate.getFullYear(), currDate.getMonth() + 1, 0).getDate();
            const effectivePaySelected =
                currDate.getDate() > 15
                    ? `${currDate.getMonth()}/1/${currDate.getFullYear()} - ${currDate.getMonth()}/15/${currDate.getFullYear()}`
                    : `${currDate.getMonth()}/16/${currDate.getFullYear()} - ${currDate.getMonth()}/${lastDay}/${currDate.getFullYear()}`;
            this.compensationService.createCompensation(
                userId,
                companyId,
                {
                    memberId: memberId,
                    createdBy: memberId,
                    positionId: positionIdUUID.Owner,
                    versionId: versionIdUUID["default"],
                    effectivePayPeriod: currDate,
                    wageAmount: 0,
                    wageInterval: WageIntervalEnum.Month,
                    payScheduleId: data.paySchedule.find((p) => p.period === 3)._id,
                    effectivePaySelected,
                    useBenchmarkBonus: false,
                },
                PermissionsEnum.Full,
            );

            //TODO: Based on plan create below data
            // company type = pro plus
            if (plan === SubscriptionPlanTypeEnum.PROPLUS) {
                const tasksUUID = {
                    "Install back pan on chimney": randomUUID(),
                    'Install new 6" chimney counterflashing': randomUUID(),
                    "Install front pan on chimney": randomUUID(),
                    "Install new step flashing on sides of chimney": randomUUID(),
                    'Cut 8" x 8" hole for can vent': randomUUID(),
                    "Cut in slot for Ridge Vent": randomUUID(),
                    "Fill vent hole with plywood": randomUUID(),
                    "Ground drop tear off": randomUUID(),
                    'Install new 7/16" OSB sheeting': randomUUID(),
                    "Install 'D' Style Drip Metal": randomUUID(),
                    "Install End Wall Flashing": randomUUID(),
                    "Install Malarkey EZ Ridge XT": randomUUID(),
                    "Pitch Transition": randomUUID(),
                    "Install Presidential Shingles": randomUUID(),
                    "Install Presidential Starter": randomUUID(),
                    "Install Presidential TL Shingles": randomUUID(),
                    "Install CT Ridge Vent": randomUUID(),
                    "Install Step Flashing": randomUUID(),
                    "Install Malarkey Secure Start SG Synthetic Felt": randomUUID(),
                    "Install TPO on flat": randomUUID(),
                    "Install Valley Metal": randomUUID(),
                    "Install 'W' Valley Metal": randomUUID(),
                    "Remove and replace existing 2x2 skylight": randomUUID(),
                    "Remove and replace existing 2x4 skylight": randomUUID(),
                    'Replace 10" B vent flashing - paint flashing, cap, & collar': randomUUID(),
                    'Replace 11" B vent flashing - paint flashing, cap, & collar': randomUUID(),
                    'Replace 12" B vent flashing - paint flashing, cap, & collar': randomUUID(),
                    'Replace 3" B vent flashing - paint flashing, cap, & collar': randomUUID(),
                    'Replace 4" B vent flashing - paint flashing, cap, & collar': randomUUID(),
                    'Replace 5" B vent flashing - paint flashing, cap, & collar': randomUUID(),
                    'Replace 6" B vent flashing - paint flashing, cap, & collar': randomUUID(),
                    'Replace 7" B vent flashing - paint flashing, cap, & collar': randomUUID(),
                    'Replace 8" B vent flashing - paint flashing, cap, & collar': randomUUID(),
                    'Replace 9" B vent flashing - paint flashing, cap, & collar': randomUUID(),
                    'Replace 1" - 3" Painted Pipe Flashing': randomUUID(),
                    'Replace 4" Painted Pipe Flashing': randomUUID(),
                    "Remove and replace existing custom sized skylight": randomUUID(),
                    'Replace 2" Split Pipe Boot Flashing': randomUUID(),
                    "Remove existing sheeting": randomUUID(),
                    "Install new back flashing on skylight": randomUUID(),
                    "Install new front flashing on skylight": randomUUID(),
                    "Install new step flashing on skylight": randomUUID(),
                    'Replace 4" stem vent': randomUUID(),
                    'Replace 6" stem vent': randomUUID(),
                    "Tear off 1 layer": randomUUID(),
                    "Tear off additional layers": randomUUID(),
                    "Add'l man hours": randomUUID(),
                    'Repair Pipe Flashing 1"-3"': randomUUID(),
                    "Replace Shingles": randomUUID(),
                    "Replace OSB/Plywood Sheets": randomUUID(),
                    "Install Ridge Vent": randomUUID(),
                    "Install/Replace Can Vents (L750)": randomUUID(),
                    "Install New Vents (RV 49)": randomUUID(),
                    "Replace/Install Roof-to-Wall Flashing": randomUUID(),
                    "Replace Step Flashing (Side-Wall)": randomUUID(),
                    "Install Kick-Out Diverters": randomUUID(),
                    "Install Back Pan Flashing": randomUUID(),
                    "Replace Chimney Counter Flashing": randomUUID(),
                    "Replace Valleys": randomUUID(),
                    "Additional Labor Hours": randomUUID(),
                    "Roof Snow Removal - Up to 2 ft deep": randomUUID(),
                    "Install Malarkey Highlander Shingles": randomUUID(),
                    "Install Malarkey Vista": randomUUID(),
                    "Install Malarkey RidgeFlex": randomUUID(),
                    "Install Malarkey SmartStart Starter": randomUUID(),
                    "Install Malarkey Legacy": randomUUID(),
                    "Install L750 Roof Vent": randomUUID(),
                    "Install LOR9-4 Ridge Vent": randomUUID(),
                    "Install Epilay Plystik Ice Shield": randomUUID(),
                    "Install Generic Synthetic Felt": randomUUID(),
                    "Remove & Replace Fascia": randomUUID(),
                    "Install LeafBlaster Pro gutter guards on all gutters": randomUUID(),
                    'Replace 8" stem vent': randomUUID(),
                    "Install Clad Metal for TPO": randomUUID(),
                    "Install Malarkey High Temp Ice Shield on entire home": randomUUID(),
                    "Extra Premium Ice Shield": randomUUID(),
                    "Extra Standard Ice Shield": randomUUID(),
                    'Install 6" Copper Moss Strip': randomUUID(),
                    "Roof Snow Removal - 2 ft+ deep": randomUUID(),
                    "Premium Ice Shield in all valleys": randomUUID(),
                    "Replace Ridge Cap": randomUUID(),
                    "Replace Shingles (per bnd)": randomUUID(),
                    "Replace Starter Shingles": randomUUID(),
                    "Replace Ice Shield ": randomUUID(),
                    "Replace Synthetic Underlayment": randomUUID(),
                    "Replace Electric Power Mast Flashing (zipper)": randomUUID(),
                    'Replace Pipe Flashing (4")': randomUUID(),
                    'R&R 4" Stem Vent': randomUUID(),
                    'R&R 6" Stem Vent': randomUUID(),
                    'Install New 4" Stem Vent': randomUUID(),
                    'Install New 6" Stem Vent': randomUUID(),
                    'Replace B-vent Flashing (up to 8")': randomUUID(),
                    'Replace B-Vent Cap & Collar (up to 8")': randomUUID(),
                    "Reflash 2'x2' Chimney": randomUUID(),
                    "Install Permanent Anchor": randomUUID(),
                    "Install Permanent Anchor-repair": randomUUID(),
                    "Reflash 2'x6' Chimney": randomUUID(),
                    "Replace Fascia Wrap": randomUUID(),
                    "Tune up metal roof (Classic rib)": randomUUID(),
                    "Replace Drip Edge Flashing": randomUUID(),
                    'Install 8"x16" Soffit Vent-Repair': randomUUID(),
                    "Install mono ridge peak cap": randomUUID(),
                    "Replace Power Attic Fan": randomUUID(),
                    "Upgrade To Emerald Premium Warranty": randomUUID(),
                    "Install Malarkey High-Temp Ice Shield": randomUUID(),
                    "Extra Malarkey High-Temp Ice Shield": randomUUID(),
                    "Malarkey High-Temp Ice Shield In Valleys": randomUUID(),
                    "Replace Shingles (PER SHINGLE)": randomUUID(),
                    "Replace Ridge Cap (PER LF)": randomUUID(),
                    "Replace Skylight 2'x2'": randomUUID(),
                    "Replace Skylight 2'x4'": randomUUID(),
                    "Replace Skylight 4'x4'": randomUUID(),
                    "Replace Skylight Custom (up to 4'x6')": randomUUID(),
                    "Install Snow Break": randomUUID(),
                    'Replace Fascia - 5/4" LP Smart Trim - 6" or 8"': randomUUID(),
                    "Install Chimney Cricket": randomUUID(),
                    "Tarp Roof Sections (SF)": randomUUID(),
                    "Remove and Reinstall Gutters": randomUUID(),
                    "Install Smart Vent Air Intake System": randomUUID(),
                    'Install 8"x16" Soffit Vent-replacement': randomUUID(),
                    "Remove/Install TPO in Dead Valley (SF)": randomUUID(),
                    "Remove & Install Siding": randomUUID(),
                    "Install Roofing on Eyebrow": randomUUID(),
                    "Install Roofing on Bay Window": randomUUID(),
                    // New added
                    "Replace Fascia Wrap Replacement": randomUUID(),
                    'Install 6" Copper Moss Strip On All Ridges': randomUUID(),
                    "Replace Ice Shield": randomUUID(),
                    "Additional Materials Repair": randomUUID(),
                    "Additional Materials": randomUUID(),
                    'Install New 10" Sun Tunnel Repair': randomUUID(),
                    'Install New 14" Sun Tunnel Repair': randomUUID(),
                    'Install New 22" Sun Tunnel Repair': randomUUID(),
                    'Install New 10" Sun Tunnel': randomUUID(),
                    'Install New 14" Sun Tunnel': randomUUID(),
                    'Install New 22" Sun Tunnel': randomUUID(),
                    "Block Gable Vents": randomUUID(),
                    "Roof Cleaning": randomUUID(),
                    "Replace OSB per sheet": randomUUID(),
                    "Install Corner Trim": randomUUID(),
                    "Install Window & Door Trim": randomUUID(),
                    "Install Board & Batten": randomUUID(),
                    "Install Fiber Cement Shake Shingles": randomUUID(),
                    "Catch-All System Home Protection": randomUUID(),
                    "Install Malarkey Windsor Shingle": randomUUID(),
                    "Install Colored Starter For Windsor": randomUUID(),
                    "Install Malarkey Permeable Synthetic": randomUUID(),
                    'Paint & Install Lead Pipe Flashing 1"-3': randomUUID(),
                    "Paint & Install Lead Pipe Flashing 4": randomUUID(),
                    "Paint & Install Split Lead Pipe Flashing 2": randomUUID(),
                    "Priority Scheduling": randomUUID(),
                    "Roof Maintenance 5 years": randomUUID(),
                    "Remove & Reset Siding to install flashing": randomUUID(),
                    "Equipter On-Site Full Time": randomUUID(),
                    "Install Band Board": randomUUID(),
                    "Remove & Replace Gutter Helmet": randomUUID(),
                    "Install Chimney Cricket/Saddle": randomUUID(),
                    //
                    "Install Standard Synthetic Felt": randomUUID(),
                    "Install Standard Ice Shield": randomUUID(),
                    "Install New Gutters & Downspouts": randomUUID(),
                    'Paint & Install Lead Pipe Flashing 1"-3"': randomUUID(),
                    'Paint & Install Lead Pipe Flashing 4"': randomUUID(),
                    'Paint & Install Split Lead Pipe Flashing 2"': randomUUID(),
                    "Install Lomanco Deck Air Intake Vent": randomUUID(),
                    "Gutter Trip Fee": randomUUID(),
                    "Gutters - Add Wedges": randomUUID(),
                    "Gutters - 1' O.C. Hangers": randomUUID(),
                    'Gutters - Upgrade to 5" Steel': randomUUID(),
                    "Upgrade To Emerald Pro Warranty": randomUUID(),
                    '4" Ultimate Pipe Flashing': randomUUID(),
                    '3" Ultimate Pipe Flashing': randomUUID(),
                    '2" Ultimate Pipe Flashing': randomUUID(),
                    '1 1/4" Ultimate Pipe Flashing': randomUUID(),
                    '1 1/2" Ultimate Pipe Flashing': randomUUID(),
                };

                const materialUUID = {
                    '10" B vent cap': randomUUID(),
                    '11" B vent cap': randomUUID(),
                    '12" B vent cap': randomUUID(),
                    '3" B vent cap': randomUUID(),
                    '4" B vent cap': randomUUID(),
                    '5" B vent cap': randomUUID(),
                    '6" B vent cap': randomUUID(),
                    '7" B vent cap': randomUUID(),
                    '8" B vent cap': randomUUID(),
                    '9" B vent cap': randomUUID(),
                    '10" B vent storm collar': randomUUID(),
                    '11" B vent storm collar': randomUUID(),
                    '12" B vent storm collar': randomUUID(),
                    '3" B vent storm collar': randomUUID(),
                    '4" B vent storm collar': randomUUID(),
                    '5" B vent storm collar': randomUUID(),
                    '6" B vent storm collar': randomUUID(),
                    '7" B vent storm collar': randomUUID(),
                    '8" B vent storm collar': randomUUID(),
                    '9" B vent storm collar': randomUUID(),
                    '10" B vent flashing': randomUUID(),
                    '11" B vent flashing': randomUUID(),
                    '12" B vent flashing': randomUUID(),
                    '3" B vent flashing': randomUUID(),
                    '4" B vent flashing': randomUUID(),
                    '5" B vent flashing': randomUUID(),
                    '6" B vent flashing': randomUUID(),
                    '7" B vent flashing': randomUUID(),
                    '8" B vent flashing': randomUUID(),
                    '9" B vent flashing': randomUUID(),
                    "CT Carriage House": randomUUID(),
                    "6\" Chimney Counterflashing 10'": randomUUID(),
                    'Coil Nails 1 1/2"': randomUUID(),
                    'Coil Nails 1 1/4"': randomUUID(),
                    'Coil Nails 1 3/4"': randomUUID(),
                    "Dump Fee": randomUUID(),
                    "Epilay Plystik Plus Ice Shield": randomUUID(),
                    "TPO 8 X 100 60mil": randomUUID(),
                    '2.4" Barbed Seam Plate': randomUUID(),
                    '#14 HD DP Fastener 3"': randomUUID(),
                    'End Wall (Roof-to-wall) Flashing 4"x4"x10\'': randomUUID(),
                    "Flex-O Seal Clear": randomUUID(),
                    "CT Flintlastic Cap": randomUUID(),
                    "CT Flintlastic Mid Ply": randomUUID(),
                    "CT Grand Manor": randomUUID(),
                    '2 1/2" Nails For Ridge': randomUUID(),
                    "CT High Performance Starter": randomUUID(),
                    "Karnak Rubber Flashing Cement": randomUUID(),
                    "CT Landmark": randomUUID(),
                    "Landmark PRO": randomUUID(),
                    '7/16" OSB sheeting': randomUUID(),
                    'Pipe Flashing 1 1/2"': randomUUID(),
                    'Pipe Flashing Combo 1"-3"': randomUUID(),
                    'Pipe Flashing 2"': randomUUID(),
                    'Pipe Flashing 3"': randomUUID(),
                    'Pipe Flashing 4"': randomUUID(),
                    "CT Presidential Starter": randomUUID(),
                    Presidential: randomUUID(),
                    'CT Mountain Ridge 10"': randomUUID(),
                    "CT Shangle Ridge": randomUUID(),
                    "CT Shadow Ridge": randomUUID(),
                    'Side Wall Flashing 4"x4"x14"': randomUUID(),
                    "Custom sized skylight": randomUUID(),
                    "Stinger StaplePacs": randomUUID(),
                    "CT Swift Start": randomUUID(),
                    "Valley Metal 14\"x50' Galvanized": randomUUID(),
                    'Valley Metal 20" Galvanized': randomUUID(),
                    "PRV 50": randomUUID(),
                    "RV 49": randomUUID(),
                    "RVO 38": randomUUID(),
                    "Shinglevent II 9\"x4' - 72 NFA": randomUUID(),
                    "Velux 2x2 Skylight": randomUUID(),
                    "Velux 2x4 Skylight": randomUUID(),
                    "Presidential TL": randomUUID(),
                    "Landmark Premium": randomUUID(),
                    "Landmark TL": randomUUID(),
                    "Malarkey Secure Start SG Underlayment": randomUUID(),
                    '4" Stem vent (metal)': randomUUID(),
                    '6" Stem vent (metal)': randomUUID(),
                    "Drip Edge D Metal": randomUUID(),
                    'Valley Metal 14" Color': randomUUID(),
                    'Valley Metal 20" Color': randomUUID(),
                    "W' Valley Metal 24\"": randomUUID(),
                    "Malarkey Vista AR": randomUUID(),
                    "Malarkey Highlander Nex AR": randomUUID(),
                    "Malarkey RidgeFlex H&R": randomUUID(),
                    "Malarkey SmartStart Starter": randomUUID(),
                    "Malarkey Legacy Shingles": randomUUID(),
                    "Lomanco LOR9-4 Ridge Vent": randomUUID(),
                    "Lomanco L750 Roof Vent": randomUUID(),
                    'Split Pipe Boot 2"': randomUUID(),
                    "Malarkey EZ Ridge XT": randomUUID(),
                    "GAF Timberline HD": randomUUID(),
                    "GAF Z-Ridge": randomUUID(),
                    "GAF Pro-Start Starter": randomUUID(),
                    '5"x13"x10\' Chimney Saddle (Backpan)': randomUUID(),
                    "Side Wall Flashing Flat 8x8": randomUUID(),
                    "Generic Ice Shield": randomUUID(),
                    "Generic Synthetic Felt": randomUUID(),
                    "Plywood vent plug": randomUUID(),
                    "5/4x8 - 16 LP Prime Trim": randomUUID(),
                    '2-1/4" Hot-Dipped Steel Siding Nails': randomUUID(),
                    "White exterior paintable caulk": randomUUID(),
                    '14" Sun Tunnel Kit - Impact Dome': randomUUID(),
                    '10" Sun Tunnel Kit - Impact Dome': randomUUID(),
                    "Gutters including d/s & elbows": randomUUID(),
                    'LeafBlaster Pro 5" gutter guards': randomUUID(),
                    "Malarkey Emerald Pro Warranty": randomUUID(),
                    '8" Stem Vent - painted, metal': randomUUID(),
                    "TPO Clad Metal": randomUUID(),
                    "Malarkey Arctic Seal Ice Shield": randomUUID(),
                    "Malarkey High Temp Ice Shield": randomUUID(),
                    '6" Copper Strip': randomUUID(),
                    '1 1/4" Copper Nails': randomUUID(),
                    "Ducting for stem vent": randomUUID(),
                    "Permanent Anchor": randomUUID(),
                    "Metal fascia wrap": randomUUID(),
                    "Butyl Tape - 50 ft roll": randomUUID(),
                    '8"x16" Painted Soffit Vent': randomUUID(),
                    "Mono Ridge Peak Cap": randomUUID(),
                    'Painted Pipe Flashing Combo 1"-3"': randomUUID(),
                    'Painted Pipe Flashing Combo 3"-4"': randomUUID(),
                    "Powered Attic Fan": randomUUID(),
                    "Malarkey Emerald Premium Warranty": randomUUID(),
                    "Velux 4'x4' Skylight": randomUUID(),
                    "Snow Break": randomUUID(),
                    'LP Smart Trim 5/4"x8"x16\'': randomUUID(),
                    '2 3/16" Siding/Fascia Nails': randomUUID(),
                    '2"x4"x8\' stud': randomUUID(),
                    "Tarps by the SF": randomUUID(),
                    '1"x2"x8\' Wood Strips': randomUUID(),
                    "Smart Vent Air Intake 4'": randomUUID(),
                    'Allura Cedar Lap 7" Primed': randomUUID(),
                    "Homeguard House Wrap 9' X 150'": randomUUID(),
                    "Tyvek Tape 3\" x 165' Clear": randomUUID(),
                    'Allura Freeze Board 4/4"x2"x12\'': randomUUID(),
                    'Allura Trim 5/4"x4"x12\' Primed': randomUUID(),
                    //new added
                    "Dynaflex Ultra DAP White exterior paintable caulk": randomUUID(),
                    '1 1/2" gasket screws': randomUUID(),
                    "Allura FC Textured Lap 8.25\"x12' Primed": randomUUID(),
                    "Additional Material Cost": randomUUID(),
                    '22" Sun Tunnel Kit': randomUUID(),
                    "Roof Wash Moss Remover": randomUUID(),
                    'Allura Trim 5/4"x3"x12\' Primed': randomUUID(),
                    "Z-trim flashing": randomUUID(),
                    "Vycor Plus 4\"x75' Flashing Tape": randomUUID(),
                    "Allura Textured Panel 4'x8'": randomUUID(),
                    "Allura Shake Shingles Staggered": randomUUID(),
                    "Hemlock Trim for Skylights": randomUUID(),
                    "Catch-All System": randomUUID(),
                    "Malarkey Windsor Shingle": randomUUID(),
                    "Malarkey Colored Starter For Windsor": randomUUID(),
                    "Malarkey Permeable Synthetic Felt": randomUUID(),
                    "Lead Pipe Flashing & Cap 1 1/2": randomUUID(),
                    "Lead Pipe Flashing & Cap 2": randomUUID(),
                    "Lead Pipe Flashing & Cap 3": randomUUID(),
                    "Lead Pipe Flashing & Cap 4": randomUUID(),
                    "Flashing Paint": randomUUID(),
                    "Lead Pipe Flashing Split Boot 2": randomUUID(),
                    "Priority Scheduling": randomUUID(),
                    "Roof Maintenance 5 years": randomUUID(),
                    "Equipter Full Time": randomUUID(),
                    'Allura Trim 5/4"x6"x12\' Primed': randomUUID(),
                    'Allura Trim 5/4"x8"x12\' Primed': randomUUID(),
                    //
                    "Standard Ice Shield": randomUUID(),
                    "Standard Synthetic Felt": randomUUID(),
                    'Lead Pipe Flashing & Cap 1 1/2"': randomUUID(),
                    'Lead Pipe Flashing & Cap 2"': randomUUID(),
                    'Lead Pipe Flashing & Cap 3"': randomUUID(),
                    'Lead Pipe Flashing & Cap 4"': randomUUID(),
                    'Lead Pipe Flashing Split Boot 2"': randomUUID(),
                    "Lomanco DA-4 Deck-Air Intake Vent": randomUUID(),
                    "Gutter Trip Fee": randomUUID(),
                    "Gutters - Add wedges": randomUUID(),
                    "Gutters - 1' O.C Hangers": randomUUID(),
                    "Gutters - Upgrade to Steel": randomUUID(),
                    '1 1/4" Ultimate Pipe Flashing': randomUUID(),
                    '1 1/2" Ultimate Pipe Flashing': randomUUID(),
                    '2" Ultimate Pipe Flashing': randomUUID(),
                    '3" Ultimate Pipe Flashing': randomUUID(),
                    '4" Ultimate Pipe Flashing': randomUUID(),
                };

                const inputUUID = {
                    '10" B vent': randomUUID(),
                    '11" B vent': randomUUID(),
                    '12" B vent': randomUUID(),
                    '3" B vent': randomUUID(),
                    '4" B vent': randomUUID(),
                    '5" B vent': randomUUID(),
                    '6" B vent': randomUUID(),
                    '7" B vent': randomUUID(),
                    '8" B vent': randomUUID(),
                    '9" B vent': randomUUID(),
                    "Existing can vents": randomUUID(),
                    "Total can vents needed": randomUUID(),
                    "Number of chimneys": randomUUID(),
                    "Back of chimney": randomUUID(),
                    "Front of chimney": randomUUID(),
                    "Both sides of chimney": randomUUID(),
                    "Number of standard curb mount 2x2 skylights": randomUUID(),
                    "Number of standard curb mount 2x4 skylights": randomUUID(),
                    Eaves: randomUUID(),
                    "End Wall": randomUUID(),
                    Hips: randomUUID(),
                    "Ice Shield": randomUUID(),
                    "Install roofing on flat": randomUUID(),
                    "Install roofing on low slope": randomUUID(),
                    "Install plywood sheeting": randomUUID(),
                    "Install roofing on steep": randomUUID(),
                    "No dump access to roof area": randomUUID(),
                    'Replace 1"-3" Pipe Flashing': randomUUID(),
                    'Replace 4" Pipe Flashing': randomUUID(),
                    "Pitch Change": randomUUID(),
                    Rakes: randomUUID(),
                    "Existing ridge vent": randomUUID(),
                    Ridges: randomUUID(),
                    "Remove roofing on flat": randomUUID(),
                    "Remove additional layers on flat": randomUUID(),
                    "Remove roofing on low slope": randomUUID(),
                    "Remove additional layers on low slope": randomUUID(),
                    "Remove existing plywood sheeting": randomUUID(),
                    "Remove roofing on steep": randomUUID(),
                    "Remove additional layers on steep": randomUUID(),
                    "Side Wall (Step)": randomUUID(),
                    "Number of skylights": randomUUID(),
                    "Back of skylights": randomUUID(),
                    "Number of custom skylights": randomUUID(),
                    "Front of skylights": randomUUID(),
                    "Both sides of skylights": randomUUID(),
                    'Replace 2" Split Pipe Boot': randomUUID(),
                    '4" Stem vent': randomUUID(),
                    '6" Stem vent': randomUUID(),
                    "Frail Input": randomUUID(),
                    Valleys: randomUUID(),
                    "Add'l man hours": randomUUID(),
                    "Replace Shingles": randomUUID(),
                    'Repair Pipe Flashings 1"-3"': randomUUID(),
                    "Replace Plywood Sheets": randomUUID(),
                    "Replace/Install Ridge Vent": randomUUID(),
                    "Replace/Install Can Vents": randomUUID(),
                    "Install New Vents": randomUUID(),
                    "Replace/Install Roof-to-Wall Flashing": randomUUID(),
                    "Replace/Install Step Flashing": randomUUID(),
                    "Install Kick-Out Diverters": randomUUID(),
                    "Install Back Pan Flashing": randomUUID(),
                    "Replace/Install Chimney Counter Flashing": randomUUID(),
                    "Replace Valleys": randomUUID(),
                    "Additional Labor Hours": randomUUID(),
                    "Roof Snow Removal (0-2 ft) Add SF for driveways/walkways": randomUUID(),
                    "Gutter Downspouts": randomUUID(),
                    '8" Stem vent': randomUUID(),
                    "Clad Metal for TPO Flat Roofing": randomUUID(),
                    "Gutter Eaves": randomUUID(),
                    "Extra Ice Shield **SQ**": randomUUID(),
                    "Copper moss strip": randomUUID(),
                    "Roof Snow Removal (2 ft+) Add SF for driveways/walkways": randomUUID(),
                    "Replace Ridge Cap": randomUUID(),
                    "Replace Shingles (bnd)": randomUUID(),
                    "Replace Starter Shingles": randomUUID(),
                    "Replace Ice Shield": randomUUID(),
                    "Replace Synthetic Underlayment": randomUUID(),
                    "Replace Power Mast Flashing (Zipper)": randomUUID(),
                    'Replace Pipe Flashing (4")': randomUUID(),
                    'R&R 4" Stem Vent': randomUUID(),
                    'R&R 6" Stem Vent': randomUUID(),
                    'Install New 4" Stem Vent': randomUUID(),
                    'Install New 6" Stem Vent': randomUUID(),
                    'Replace B-vent Flashing (up to 8")': randomUUID(),
                    'Replace B-Vent Cap & Collar (up to 8")': randomUUID(),
                    "Reflash 2'x2' Chimney/Skylight": randomUUID(),
                    "Reflash 2'x6' Chimney/Skylight": randomUUID(),
                    "Replace Fascia Wrap w/ Metal": randomUUID(),
                    "Tune up metal roof (Classic rib)": randomUUID(),
                    "Replace Drip Edge Flashing": randomUUID(),
                    'Install 8"x16" soffit vent': randomUUID(),
                    "Mono ridge peak cap": randomUUID(),
                    "Replace Power Attic Vent": randomUUID(),
                    "Replace shingles (PER SHINGLE)": randomUUID(),
                    "Replace Ridge Cap (PER LF)": randomUUID(),
                    "Replace Skylight 2'x2'": randomUUID(),
                    "Replace Skylight 2'x4'": randomUUID(),
                    "Replace Skylight 4'x4'": randomUUID(),
                    "Replace Skylight Custom (up to 4'x6')": randomUUID(),
                    "Install Snow Break": randomUUID(),
                    'Replace Fascia - 5/4" LP Smart Trim - 6" or 8"': randomUUID(),
                    "Install Chimney Cricket/Saddle": randomUUID(),
                    "Tarp Roof Sections": randomUUID(),
                    "Install Permanent Anchor": randomUUID(),
                    "Install Permanent Anchor-repair": randomUUID(),
                    "Remove and Reinstall Gutters": randomUUID(),
                    "Install Smart Vent Air Intake": randomUUID(),
                    'Install 8"x16" Soffit Vent-replacement': randomUUID(),
                    "Install TPO in Dead Valley": randomUUID(),
                    "Siding SQ": randomUUID(),
                    "Siding Trim": randomUUID(),
                    Eyebrows: randomUUID(),
                    "Bay Windows": randomUUID(),
                    // new added
                    "Add'l Materials Repair": randomUUID(),
                    "Add'l Materials": randomUUID(),
                    'Install 10" Sun Tunnel Repair': randomUUID(),
                    'Install 14" Sun Tunnel Repair': randomUUID(),
                    'Install 22" Sun Tunnel Repair': randomUUID(),
                    'Install 10" Sun Tunnel': randomUUID(),
                    'Install 14" Sun Tunnel': randomUUID(),
                    'Install 22" Sun Tunnel': randomUUID(),
                    "Block Gable Vents": randomUUID(),
                    "Roof Cleaning": randomUUID(),
                    "Replace plywood per sheet": randomUUID(),
                    "Siding Corners": randomUUID(),
                    "Siding Window & Door Trim": randomUUID(),
                    "Siding Board & Batten": randomUUID(),
                    "Siding Shake Shingles": randomUUID(),
                    "Siding Band Board": randomUUID(),
                    "Remove & Replace Gutter Helmet": randomUUID(),
                    "Replace Fascia Wrap w/ Metal Replacement": randomUUID(),
                    "Remove & Replace Gutter Guards": randomUUID(),
                    "Gutter Guards": randomUUID(),
                    "Install Deck Intake Vent": randomUUID(),
                    "Gutter Trip Fee": randomUUID(),
                    "Gutters - Add Wedges": randomUUID(),
                    "Gutters - 1' O.C. Hangers": randomUUID(),
                    "Gutters - Upgrade to Steel": randomUUID(),
                    '1 1/4" Pipe Flashing': randomUUID(),
                    '1 1/2" Pipe Flashing': randomUUID(),
                    '2" Pipe Flashing': randomUUID(),
                    '3" Pipe Flashing': randomUUID(),
                };

                const packagesUUID = {
                    "Vista Package": randomUUID(),
                };

                await Promise.all([
                    this.crmService.addDefaultSalesActionSetting(companyId, memberId),
                    this.projectService.addDefaultTaxJurisdictions(companyId, memberId),
                    this.crmService.addDefaultCRM(companyId, {
                        createdBy: memberId,
                        projectType: {
                            repairType: projectTypeUUID.repair,
                            replacementType: projectTypeUUID.replacement,
                        },
                    }),
                    this.projectService.addInputSetting(
                        companyId,
                        memberId,
                        inputUUID,
                        projectTypeUUID,
                        unitUUID,
                    ),
                    this.projectService.addDefaultTasks(
                        companyId,
                        memberId,
                        inputUUID,
                        materialUUID,
                        crewPositionUUID,
                        tasksUUID,
                        projectTypeUUID,
                        unitUUID,
                        taskGroup,
                    ),
                    this.projectService.addDefaultPackages(
                        companyId,
                        memberId,
                        tasksUUID,
                        projectTypeUUID,
                        packagesUUID,
                    ),
                    this.projectService.addDefaultOptions(
                        companyId,
                        memberId,
                        tasksUUID,
                        projectTypeUUID,
                        packagesUUID,
                        taskGroup,
                    ),
                    this.projectService.addDefaultMaterial(
                        companyId,
                        memberId,
                        materialUUID,
                        categoryIdUUID,
                        subCategoryIdUUID,
                        unitUUID,
                    ),
                    this.createDefaultMediaSetting(companyId, defaultMediaSetting),
                ]);
            }

            return { positionId: positionIdUUID.Owner };
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createCompany(userId: string, createCompanyDto: CreateCompanyDto, name: string, email: string) {
        let session;
        try {
            // Starting session for Mongo transaction
            session = await this.connection.startSession();
            session.startTransaction();

            const memberId = randomUUID();
            const roleId = randomUUID();
            const companyId = randomUUID();

            const user = await this.userService.getUserById(userId);
            if (!user) throw new HttpException("User not exists", HttpStatus.NOT_FOUND);

            // Creating Stripe customer
            const stripeCustomerId = await this.stripeService.createCustomer({
                name: createCompanyDto.companyName,
                email,
            });

            //creating member
            const createdMember = new this.memberModel({
                _id: memberId,
                email: user.email,
                name,
                user: user._id,
                company: companyId,
                hireDate: new Date(),
                roleId,
            });
            await createdMember.save({ session });

            //updating user
            await this.userModel.updateOne(
                { _id: user._id },
                { $set: { stripeCustomerId }, $push: { members: memberId } },
                { session },
            );

            // const company = await this.companyModel
            //     .findOne({ companyName: createCompanyDto.companyName })
            //     .exec();
            // if (company) throw new HttpException("Company already exists", HttpStatus.BAD_REQUEST);

            const stripePlans = await this.subscriptionPlanModel
                .findOne({
                    isDefault: true,
                })
                .populate("defaultPrice", "", this.subscriptionPriceModel);

            const createdCompany = new this.companyModel({
                _id: companyId,
                ...createCompanyDto,
                owner: userId,
                planType: SubscriptionPlanTypeEnum.FREE,
                planId: stripePlans._id,
                stripeCustomerId: stripeCustomerId,
                cancelAt: null,
            });
            await createdCompany.save({ session });

            // creating company analatyics
            this.companyAnalyticsService.createComapnyAnalytics(companyId, {
                subTeamSize: 1,
            });
            // ending session of mongodb
            await session.commitTransaction();
            session.endSession();

            const { positionId } = await this.addCompanyDefaultData(
                userId,
                companyId,
                createCompanyDto.companyName,
                SubscriptionPlanTypeEnum.FREE,
                memberId,
            );

            const { success } = await this.stripeService.paymentIntentWithoutCard(
                stripeCustomerId,
                stripePlans.defaultPrice.stripePriceId,
            );

            //saving member role
            this.roleService.createMemberRole(companyId, {
                _id: roleId,
                memberId: memberId,
                createdBy: memberId,
                role: UserRolesEnum.Owner,
            });

            return { memberId, companyId, planType: SubscriptionPlanTypeEnum.FREE, positionId };
        } catch (error: any) {
            await session.abortTransaction();
            session.endSession();
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateCompany(companyId: string, updateCompanyDto: UpdateCompanyDto) {
        try {
            const { companyName, foundingYear, imageUrl, workType } = updateCompanyDto;
            const result = await this.companyModel.updateOne(
                { _id: companyId },
                {
                    $set: {
                        companyName,
                        foundingYear,
                        imageUrl,
                        workType,
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new BadRequestException({ message: "Failed to update changes!" });
            }
            return new OkResponse({ message: "Company updated" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getCompanyById(isAdmin: boolean, companyId: string) {
        try {
            const query = isAdmin ? { _id: companyId } : { _id: companyId, deleted: { $ne: true } };
            const company = await this.companyModel
                .findOne(query)
                .populate(
                    "owner",
                    "firstName lastName email stripeCustomerId phone members createdAt",
                    "User",
                )
                .populate("stripeSubscriptionItemId", "name ", "SubscriptionPlans");

            if (!company) throw new NotFoundException("Company does not exist");

            // stripeSubscriptionItemId
            return new OkResponse({ company });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getCompanies(deleted: boolean) {
        try {
            const companies = await this.companyModel
                .find({ deleted })
                .populate("owner", "email stripeCustomerId phone", "User");
            return new OkResponse({ companies });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restoreCompany(companyId: string) {
        try {
            const company = await this.companyModel.exists({ _id: companyId, deleted: true });
            if (!company) throw new NotFoundException("The specified company does not exist.");
            const result = await this.companyModel.updateOne(
                { _id: companyId },
                { $set: { deleted: false } },
            );
            if (result.modifiedCount)
                return new OkResponse({ message: "The company was successfully restored." });
            else throw new ConflictException("Failed to update the company status.");
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async softDeleteCompany(companyId: string) {
        try {
            const company = await this.companyModel.exists({ _id: companyId });
            if (!company) throw new NotFoundException("The specified company does not exist.");
            const result = await this.companyModel.updateOne(
                { _id: companyId },
                { $set: { deleted: true, deletedAt: new Date() } },
            );
            if (result.modifiedCount)
                return new OkResponse({ message: "The company was successfully marked as deleted." });
            else throw new ConflictException("Failed to update the company status.");
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async permDeleteCompany(allIds: string[]) {
        let session;
        try {
            const companies = await this.companyModel
                .find({ _id: { $in: allIds } })
                .select("companyName deleted deletedAt");

            const ids = [];
            const notDeleted = [];
            const less15days = [];

            companies.forEach((c) => {
                if (!c.deleted) {
                    notDeleted.push(c.companyName);
                    return;
                }

                const daysSinceDeleted =
                    (new Date().getTime() - c.deletedAt.getTime()) / (1000 * 60 * 60 * 24);
                if (daysSinceDeleted > 15) {
                    ids.push(c._id);
                } else {
                    less15days.push(c.companyName);
                }
            });

            // Construct the response message
            let message = "Nothing to deleted.";

            if (ids.length > 0) message = "Company and its data deleted permanently.";

            if (notDeleted.length > 0)
                message += `\nThe following companies are not marked as deleted and cannot be deleted: ${notDeleted.join(
                    ", ",
                )}.`;

            if (less15days.length > 0)
                message += `\nThe following companies were deleted less than 15 days ago and cannot be deleted: ${less15days.join(
                    ", ",
                )}.`;

            if (ids.length > 0) {
                // Starting session for Mongo transaction
                session = await this.connection.startSession();
                session.startTransaction();

                await Promise.all([
                    // A
                    // await this.activityLogModel.deleteMany({ companyId: { $in: ids } }, { session });

                    // C
                    this.categoryModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.cityModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.clientModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.commissionModificationModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.companyModel.deleteMany({ _id: { $in: ids } }, { session }),
                    this.companyAnalyticsModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.companyPayModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.companySettingModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.compensationModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.contractsModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.crewModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.crewMemberModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.crewPositionModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.crmCheckpointModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.crmStageModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.crmStepModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.customProjectModel.deleteMany({ companyId: { $in: ids } }, { session }),

                    // D
                    this.dailyLogModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.departmentsModel.deleteMany({ companyId: { $in: ids } }, { session }),

                    // G
                    this.gpsModel.deleteMany({ companyId: { $in: ids } }, { session }),

                    // I
                    this.inputModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.invitationsModel.deleteMany({ company: { $in: ids } }, { session }),

                    // L
                    this.leadModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.leadSourcesModel.deleteMany({ companyId: { $in: ids } }, { session }),

                    // M
                    this.marketingModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.materialModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.mediaSettingModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.membersModel.deleteMany({ company: { $in: ids } }, { session }),

                    // O
                    this.opportunityModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.activityModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.optionsModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.orderModel.deleteMany({ companyId: { $in: ids } }, { session }),

                    // P
                    this.packageModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.payrollModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.payScheduleModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.pieceWorkModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.pieceWorkSettingModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.positionModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.priceModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.projectModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.projectTypeModel.deleteMany({ companyId: { $in: ids } }, { session }),

                    // R
                    this.roleModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.referrersModel.deleteMany({ companyId: { $in: ids } }, { session }),

                    // S
                    this.salesActionModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.subCategoryModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.subContractorModel.deleteMany({ companyId: { $in: ids } }, { session }),

                    // T
                    this.taskModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.taxModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    this.timeCardModel.deleteMany({ companyId: { $in: ids } }, { session }),

                    // U & W
                    this.unitModel.deleteMany({ companyId: { $in: ids } }, { session }),
                    // this.userModel.deleteMany({ companyId: { $in: ids } }, { session }), // do not delete user remove member ids only
                    this.worktasksModel.deleteMany({ companyId: { $in: ids } }, { session }),
                ]);

                await session.commitTransaction();
                session.endSession();
            }

            return new OkResponse({ message });
        } catch (error) {
            await session.abortTransaction();
            session.endSession();
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async checkPlanType(userId: string, companyId: string, requiredPlans: any[]) {
        try {
            const company = await this.companyModel.findOne({ _id: companyId }).select("planType status");

            if (company.status === SubscriptionStatusEnum.ACTIVE && requiredPlans.includes(company.planType))
                return { hasPlan: true, planType: company.planType };
            else return { hasPlan: false, planType: company.planType };
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createMember(userId: string, companyId: string, createMemberDto: CreateMemberDto) {
        try {
            const company = await this.companyModel.exists({ _id: companyId, deleted: { $ne: true } });
            if (!company) throw new NotFoundException("Company does not exist");

            const roleId = randomUUID();
            const existingMember = await this.memberModel
                .findOne({
                    email: createMemberDto.email,
                    user: userId,
                    company: companyId,
                    deleted: false,
                })
                .exec();
            if (existingMember) throw new HttpException("Member already exists", HttpStatus.BAD_REQUEST);
            const createdMember = new this.memberModel({
                email: createMemberDto.email,
                user: userId,
                company: companyId,
                invited: createMemberDto.invited,
                roleId,
                name: createMemberDto.name,
                preferredName: createMemberDto?.preferredName || "",
                hireDate: createMemberDto?.hireDate,
                departmentId: createMemberDto?.departmentId,
                managerId: createMemberDto?.managerId,
                subContractorId: createMemberDto?.subContractorId,
            });
            await createdMember.save();
            await this.userModel.updateOne({ _id: userId }, { $push: { members: createdMember._id } });

            this.roleService.createMemberRole(companyId, {
                _id: roleId,
                memberId: createdMember._id,
                createdBy: createdMember._id,
                role: UserRolesEnum.Member,
            });
            return new CreatedResponse({
                memberId: createdMember._id,
                message: "Member created successfully!",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateMemberCompanyInfo(
        userId: string,
        companyId: string,
        updateMemberCompanyInfoDto: UpdateMemberCompanyInfoDto,
        teamPermission: number,
    ) {
        try {
            let isAllowed;
            const {
                managerId,
                departmentId,
                hireDate,
                memberId,
                name,
                preferredName,
                email,
                notes,
                subContractorId,
            } = updateMemberCompanyInfoDto;
            const member = await this.memberModel
                .findOne({
                    _id: memberId,
                    deleted: false,
                })
                .exec();
            if (!member) throw new HttpException("Member does not exists", HttpStatus.BAD_REQUEST);
            if (member.managerId) {
                await this.positionService.permissionCheck(
                    userId,
                    companyId,
                    memberId, //not used in case of team permission check
                    member.managerId,
                    teamPermission,
                );
                isAllowed = true;
            }
            if (isAllowed || teamPermission === PermissionsEnum.Full) {
                await this.memberModel.findOneAndUpdate(
                    { _id: memberId },
                    {
                        $set: {
                            managerId,
                            hireDate,
                            departmentId: departmentId,
                            name,
                            preferredName,
                            email,
                            notes,
                            subContractorId,
                        },
                    },
                    { new: true },
                );

                // if name is present then update crew member name
                const memberName =
                    preferredName && preferredName !== "" ? `${preferredName} ${name.split(" ")[1]}` : name;
                if (name || preferredName)
                    await this.crewMemberModel.updateMany(
                        { memberId },
                        {
                            $set: {
                                memberName,
                                preferredName,
                            },
                        },
                    );
            } else
                throw new HttpException(
                    "You are unauthorized to perform this request",
                    HttpStatus.BAD_REQUEST,
                );
            return new OkResponse({ message: "Member updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async companyInvitationAndActiveUserCount(companyId: string) {
        try {
            const [company, memberCount, inviteCount] = await Promise.all([
                this.companyModel
                    .findOne({ _id: companyId })
                    .select("_id planType subscribedTeamSize interval")
                    .exec(),
                this.memberModel.countDocuments({
                    company: companyId,
                    deleted: false,
                }),
                this.invitationModel.countDocuments({
                    company: companyId,
                    status: { $in: [InvitationStatusEnum.Pending, InvitationStatusEnum.Resend] },
                }),
            ]);

            return new OkResponse({
                planType: company.planType,
                teamSize: company.subscribedTeamSize,
                interval: company?.interval,
                memberCount,
                inviteCount,
            });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async inviteMember(userId: string, companyId: string, inviteMemberDto: InviteMemberDto) {
        try {
            const { firstName, lastName, recipientEmail, senderEmail, phone, preferredName } =
                inviteMemberDto;

            const [user, company, activeMemberCount, member, invitation, pendingInviteCount] =
                await Promise.all([
                    this.userModel.findById(userId),
                    this.companyModel
                        .findOne({ _id: companyId })
                        .select("_id planType subscribedTeamSize companyName")
                        .exec(),
                    this.memberModel.countDocuments({
                        company: companyId,
                        deleted: false,
                    }),
                    this.memberModel
                        .findOne({
                            company: companyId,
                            email: inviteMemberDto.recipientEmail,
                            deleted: false,
                        })
                        .exec(),
                    this.invitationModel
                        .findOne({
                            email: recipientEmail,
                            company: companyId,
                            status: { $in: [InvitationStatusEnum.Pending, InvitationStatusEnum.Accepted] },
                        })
                        .exec(),
                    this.invitationModel.countDocuments({
                        company: companyId,
                        status: { $in: [InvitationStatusEnum.Pending, InvitationStatusEnum.Resend] },
                    }),
                ]);

            if (!company) throw new HttpException("Company does not exist", HttpStatus.BAD_REQUEST);
            if (company.planType === SubscriptionPlanTypeEnum.FREE)
                throw new HttpException(
                    "Subscribe to a Plan for inviting people",
                    HttpStatus.METHOD_NOT_ALLOWED,
                );
            if (
                company.interval != SubscriptionRenewalPeriod.MONTH &&
                company.subscribedTeamSize <= activeMemberCount + pendingInviteCount
            )
                throw new HttpException(
                    "Team size limit exceeded(including invited members). Unable to add more members.",
                    HttpStatus.METHOD_NOT_ALLOWED,
                );

            if (member) throw new HttpException("Member already exists", HttpStatus.BAD_REQUEST);

            if (invitation) throw new HttpException("Invitation already sent", HttpStatus.BAD_REQUEST);

            //TODO: hardcoded value if its nhr redirect to nhrapp.com else pieceworkpro.com
            const frontendBaseUrl =
                "0f33b070-a7f2-43f3-8d07-54fdfd4378e3" === company._id
                    ? this.configService.get<string>("FR_BASE_URL")
                    : this.configService.get<string>("FR_BASE_URL2");

            const _id = randomUUID();
            const resetURL = `${frontendBaseUrl}/invitation/${_id}`;
            const senderName = user?.lastName ? `${user.firstName} ${user.lastName}` : user.firstName;
            const reciverName = lastName ? `${firstName} ${lastName}` : firstName;

            if (
                !(
                    await this.mailService.sendCompanyInvitationMail(
                        recipientEmail,
                        company.companyName,
                        resetURL,
                        senderName.trim(),
                        reciverName.trim(),
                    )
                ).sent
            ) {
                throw new InternalServerErrorException("Failed to send Email!");
            }
            const createdInvitation = new this.invitationModel({
                _id,
                email: recipientEmail,
                firstName,
                lastName,
                company: companyId,
                senderEmail,
                phone,
                preferredName,
            });
            await createdInvitation.save();

            // update company analatyics
            this.companyAnalyticsService.updateCompanyTeamAnalytics(companyId, {
                invited: 1,
            });

            return new OkResponse({
                id: createdInvitation._id,
                message: "Email with invitation link has been sent!",
            });
        } catch (error: any) {
            console.log(error);

            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateInviteMemberCompensation(
        userId: string,
        companyId: string,
        id: string,
        invitationWageDto: InvitationWageDto,
    ) {
        const { managerId, hireDate, departmentId, subContractorId } = invitationWageDto;
        delete invitationWageDto?.managerId;
        delete invitationWageDto?.hireDate;
        delete invitationWageDto?.departmentId;
        delete invitationWageDto?.subContractorId;

        try {
            const invitation = await this.invitationModel
                .findOne({
                    _id: id,
                    company: companyId,
                    status: InvitationStatusEnum.Pending,
                })
                .exec();

            if (!invitation) throw new HttpException("Invitation not found", HttpStatus.BAD_REQUEST);

            const createdInvitation = await this.invitationModel.updateOne(
                { _id: id, company: companyId },
                {
                    $set: { wage: invitationWageDto, managerId, hireDate, departmentId, subContractorId },
                },
            );
            if (createdInvitation.modifiedCount > 0)
                return new OkResponse({ message: "Email with invitation link has been sent!" });
            else throw new BadRequestException({ message: "Failed to update changes!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async invitationResponse(companyId: string, invitationResponseDto: InvitationResponseDto) {
        let session;
        try {
            // Starting session for Mongo transaction
            session = await this.connection.startSession();
            session.startTransaction();

            const [user, activeMemberCount, invitation, company] = await Promise.all([
                await this.userService.getUserByUsernameOrEmailOrToken(invitationResponseDto.recipientEmail),
                this.memberModel.countDocuments({
                    company: companyId,
                    deleted: false,
                }),
                this.invitationModel
                    .findOne({
                        $or: [
                            { status: InvitationStatusEnum.Pending },
                            { status: InvitationStatusEnum.Resend },
                        ],
                        email: invitationResponseDto.recipientEmail,
                        company: companyId,
                    })
                    .exec(),
                // this.invitationModel.count({
                //     company: invitationResponseDto.company,
                //     status: { $in: [InvitationStatusEnum.Pending, InvitationStatusEnum.Resend] },
                // }),
                this.companyModel
                    .findOne({ _id: companyId })
                    .select("_id planType subscribedTeamSize companyName")
                    .exec(),
            ]);

            if (
                company.interval !== SubscriptionRenewalPeriod.MONTH &&
                company.subscribedTeamSize <= activeMemberCount
            )
                throw new HttpException(
                    "Team size limit exceeded. Unable to add more members.",
                    HttpStatus.METHOD_NOT_ALLOWED,
                );

            if (!invitation) throw new HttpException("Invitation does not exist", HttpStatus.BAD_REQUEST);

            await this.invitationModel.updateOne(
                {
                    email: invitationResponseDto.recipientEmail,
                    companyId,
                    $or: [{ status: InvitationStatusEnum.Pending }, { status: InvitationStatusEnum.Resend }],
                },
                { $set: { status: invitationResponseDto.status } },
                { session },
            );

            if (user) {
                if (invitationResponseDto.status === 2) {
                    const isContractor = invitation?.subContractorId ? true : false;
                    const name = user?.lastName ? user.firstName + " " + user.lastName : user.firstName;

                    const {
                        data: { memberId },
                    } = await this.createMember(user._id, companyId, {
                        email: invitationResponseDto.recipientEmail,
                        user: user._id,
                        invited: true,
                        name: name.trim(),
                        hireDate: invitation?.hireDate,
                        managerId: invitation?.managerId,
                        departmentId: invitation?.departmentId,
                        preferredName: invitation?.preferredName,
                        subContractorId: invitation?.subContractorId,
                    });

                    // If member is a subcontractor then adding its Id in subcontractor
                    if (isContractor)
                        await this.subContractorModel.updateOne(
                            { _id: invitation?.subContractorId },
                            { $push: { memberIds: memberId } },
                            { session },
                        );

                    //NOTE: for internal use sending userId string
                    if (invitation?.wage && Object.keys(invitation.wage)) {
                        const wage: any = invitation.wage;
                        await this.compensationService.createCompensation(
                            user._id,
                            companyId,
                            {
                                memberId,
                                ...wage,
                            },
                            PermissionsEnum.Full,
                        );
                    }
                }

                await session.commitTransaction();
                session.endSession();

                return new OkResponse({
                    message: `The user has already been registered with ${company?.companyName}!`,
                });
            } else throw new NotFoundException({ message: "User does not exist!" });
        } catch (error: any) {
            await session.abortTransaction();
            session.endSession();
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async revokeInvitation(companyId: string, userInvitationDto: UserInvitationDto) {
        try {
            const invitation = await this.invitationModel
                .findOne({
                    _id: userInvitationDto._id,
                    company: companyId,
                    status: {
                        $in: [InvitationStatusEnum.Pending, InvitationStatusEnum.Resend],
                    },
                })
                .exec();
            if (!invitation) throw new HttpException("Invitation does not exist", HttpStatus.BAD_REQUEST);
            await this.invitationModel.updateOne(
                {
                    _id: userInvitationDto._id,
                    company: companyId,
                },
                {
                    status: InvitationStatusEnum.Revoked,
                },
            );
            return new OkResponse({ message: "Invitation revoked successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async resendInvitation(userId: string, companyId: string, userInvitationDto: UserInvitationDto) {
        try {
            const [user, company, activeMemberCount, invitation, pendingInviteCount] = await Promise.all([
                this.userModel.findById(userId),
                this.companyModel
                    .findOne({ _id: companyId })
                    .select("_id planType subscribedTeamSize companyName")
                    .exec(),
                this.memberModel.countDocuments({
                    company: companyId,
                    deleted: false,
                }),
                this.invitationModel
                    .findOne({
                        _id: userInvitationDto._id,
                        company: companyId,
                        status: {
                            $in: [
                                InvitationStatusEnum.Pending,
                                InvitationStatusEnum.Revoked,
                                InvitationStatusEnum.Rejected,
                                InvitationStatusEnum.Resend,
                            ],
                        },
                    })
                    .exec(),
                this.invitationModel.countDocuments({
                    company: companyId,
                    status: {
                        $in: [
                            // InvitationStatusEnum.Revoked,
                            InvitationStatusEnum.Resend,
                            InvitationStatusEnum.Pending,
                        ],
                    },
                }),
            ]);

            if (!invitation) throw new HttpException("Invitation does not exist", HttpStatus.BAD_REQUEST);
            if (!company) throw new HttpException("Company does not exist", HttpStatus.BAD_REQUEST);

            if (
                (invitation.status === InvitationStatusEnum.Revoked ||
                    invitation.status === InvitationStatusEnum.Rejected) &&
                company.subscribedTeamSize <= activeMemberCount + pendingInviteCount
            )
                throw new HttpException(
                    "Team size limit exceeded(including invited members). Unable to add more members.",
                    HttpStatus.METHOD_NOT_ALLOWED,
                );

            // if its nhr redirect to nhrapp.com else pieceworkpro.com
            const frontendBaseUrl =
                "0f33b070-a7f2-43f3-8d07-54fdfd4378e3" === company._id
                    ? this.configService.get<string>("FR_BASE_URL")
                    : this.configService.get<string>("FR_BASE_URL2");

            const resetURL = `${frontendBaseUrl}/invitation/${userInvitationDto._id}`;

            const senderName = user?.lastName ? `${user.firstName} ${user.lastName}` : user.firstName;
            const reciverName = invitation?.lastName
                ? `${invitation.firstName} ${invitation.lastName}`
                : invitation.firstName;

            if (
                !(
                    await this.mailService.sendCompanyInvitationMail(
                        userInvitationDto.email,
                        company.companyName,
                        resetURL,
                        senderName.trim(),
                        reciverName.trim(),
                    )
                ).sent
            ) {
                throw new InternalServerErrorException("Email sent failed");
            }
            await this.invitationModel.updateOne(
                {
                    _id: userInvitationDto._id,
                    company: companyId,
                },
                {
                    status: InvitationStatusEnum.Resend,
                },
            );
            return new OkResponse({ message: "Invitation resend successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getUserMemberAndCompany(membersArr: string[]) {
        try {
            const member = (
                await this.memberModel.aggregate([
                    { $match: { _id: { $in: membersArr }, deleted: false } },
                    {
                        $lookup: {
                            from: "Company",
                            localField: "company",
                            foreignField: "_id",
                            as: "company",
                            pipeline: [{ $project: { _id: 1, planType: 1, deleted: 1 } }],
                        },
                    },
                    {
                        $unwind: {
                            path: "$company",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Compensation",
                            localField: "_id",
                            foreignField: "memberId",
                            as: "comp",
                            pipeline: [{ $project: { positionId: 1 } }],
                        },
                    },
                    {
                        $lookup: {
                            from: "Position",
                            localField: "comp.positionId",
                            foreignField: "_id",
                            as: "position",
                            pipeline: [{ $project: { symbol: 1 } }],
                        },
                    },
                    {
                        $unwind: {
                            path: "$position",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Role",
                            foreignField: "_id",
                            localField: "roleId",
                            as: "roleData",
                        },
                    },
                    {
                        $unwind: {
                            path: "$roleData",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $project: {
                            _id: 1,
                            position: 1,
                            company: 1,
                            roleId: 1,
                            role: "$roleData.role",
                        },
                    },
                ])
            )[0];

            return { member };
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getUserCompanies(userId: string) {
        try {
            const companiesData: any[] = [];
            const userRes = await this.userService.getUserById(userId);
            if (!userRes) throw new HttpException("User does not exist", HttpStatus.BAD_REQUEST);
            const members = await this.memberModel.find({ _id: { $in: userRes.members }, deleted: false });
            // TODO: hard coded need to be removed
            if (members.length) {
                for (const member of members) {
                    const response: any = {};
                    const companies = await this.companyModel.find({ _id: { $in: member.company } });
                    response.member = member;
                    response.company = companies[0];
                    companiesData.push(response);
                }
            } else {
                const companies = await this.companyModel.find({
                    _id: "0f33b070-a7f2-43f3-8d07-54fdfd4378e3",
                });
                const response: any = {};
                response.company = companies[0];
                companiesData.push(response);
            }
            userRes.password = undefined;
            userRes.passwordResetToken = undefined;
            userRes.passwordResetExpires = undefined;
            return new OkResponse({ user: userRes, companiesData });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getCompanyMembers(
        userId: string,
        companyId: string,
        deleted: boolean,
        paginationRequestDto: PaginationRequestDto,
        teamPermission: number,
    ) {
        try {
            const limit = paginationRequestDto.limit || 10;
            const offset = limit * (paginationRequestDto.skip || 0);
            const company = await this.companyModel
                .findOne({ _id: companyId, deleted: { $ne: true } })
                .exec();
            if (!company) throw new NotFoundException("Company does not exist");
            const userMember = await this.memberModel.findOne({
                company: company._id,
                user: userId,
                deleted: false,
            });
            const query =
                teamPermission === PermissionsEnum.Managed
                    ? {
                          company: companyId,
                          deleted,
                          $or: [
                              {
                                  managerId: userMember._id,
                              },
                              {
                                  user: userId,
                              },
                          ],
                      }
                    : teamPermission === PermissionsEnum.Self
                    ? { company: companyId, deleted, user: userId }
                    : { company: companyId, deleted };

            const searchFilter = paginationRequestDto.search
                ? {
                      $or: [
                          { email: { $regex: paginationRequestDto.search, $options: "i" } },
                          { name: { $regex: paginationRequestDto.search, $options: "i" } },
                      ],
                  }
                : {};

            const sortFilter: { [key: string]: 1 | -1 } = deleted ? { terminateDate: -1 } : { createdAt: -1 };

            const members = await this.memberModel.aggregate([
                {
                    $match: { ...query, ...searchFilter, isSubContractor: { $ne: true } },
                },
                {
                    $lookup: {
                        from: "Compensation",
                        localField: "_id",
                        foreignField: "memberId",
                        as: "result",
                        pipeline: [{ $project: { positionId: 1 } }],
                    },
                },
                // {
                //     $lookup: {
                //         from: "Role",
                //         localField: "_id",
                //         foreignField: "memberId",
                //         as: "roleData",
                //     },
                // },
                // {
                //     $unwind: {
                //         path: "$roleData",
                //         preserveNullAndEmptyArrays: true,
                //     },
                // },
                {
                    $lookup: {
                        from: "Department",
                        localField: "departmentId",
                        foreignField: "_id",
                        as: "departmentDetail",
                        pipeline: [{ $project: { name: 1 } }],
                    },
                },
                {
                    $unwind: {
                        path: "$departmentDetail",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $lookup: {
                        from: "Position",
                        localField: "result.positionId",
                        foreignField: "_id",
                        as: "position",
                        pipeline: [{ $project: { position: 1 } }],
                    },
                },
                {
                    $unwind: {
                        path: "$position",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $lookup: {
                        from: "User",
                        localField: "user",
                        foreignField: "_id",
                        as: "users",
                        pipeline: [{ $project: { DOB: 1, phone: 1, imageUrl: 1 } }],
                    },
                },
                {
                    $unwind: {
                        path: "$users",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $lookup: {
                        from: "Member",
                        localField: "managerId",
                        foreignField: "_id",
                        as: "manager",
                        pipeline: [{ $project: { name: 1 } }],
                    },
                },
                {
                    $unwind: {
                        path: "$manager",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $addFields: {
                        name: {
                            $cond: {
                                if: {
                                    $and: [
                                        { $ifNull: ["$preferredName", false] },
                                        { $ne: ["$preferredName", ""] },
                                    ],
                                },
                                then: {
                                    $concat: [
                                        "$preferredName",
                                        " ",
                                        { $arrayElemAt: [{ $split: ["$name", " "] }, 1] },
                                    ],
                                },
                                else: "$name",
                            },
                        },
                        DOB: "$users.DOB",
                        phone: "$users.phone",
                        positionName: "$position.position",
                        departmentName: "$departmentDetail.name",
                        managerName: "$manager.name",
                        imageUrl: "$users.imageUrl",
                    },
                },
                { $sort: sortFilter },
                { $skip: offset },
                { $limit: limit },
                {
                    $project: {
                        name: 1,
                        email: 1,
                        hireDate: 1,
                        DOB: 1,
                        phone: 1,
                        positionName: 1,
                        departmentName: 1,
                        managerName: 1,
                        user: 1,
                        imageUrl: 1,
                        terminateDate: 1,
                        message: 1,
                    },
                },
            ]);

            const totalItems = await this.memberModel.countDocuments(query);

            return new OkResponse({ company: company, memberData: members, totalItems });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getAdminsAndOwner(companyId: string) {
        try {
            const members = await this.memberModel.aggregate([
                {
                    $match: { company: companyId, deleted: false },
                },
                {
                    $lookup: {
                        from: "Role",
                        localField: "roleId",
                        foreignField: "_id",
                        pipeline: [
                            { $match: { role: { $in: [UserRolesEnum.Admin, UserRolesEnum.Owner] } } },
                            { $project: { role: 1 } },
                        ],
                        as: "role",
                    },
                },
                {
                    $unwind: {
                        path: "$role",
                        preserveNullAndEmptyArrays: false,
                    },
                },
                {
                    $project: {
                        name: 1,
                        preferredName: 1,
                        company: 1,
                        user: 1,
                        roleId: 1,
                        role: 1,
                    },
                },
            ]);

            return new OkResponse({ members });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getCompanyMembersByPosition(
        memberId: string,
        companyId: string,
        permissions: PermissionsEnum,
        fetchMemberDto: FetchMemberDto,
    ) {
        if (permissions === PermissionsEnum.None) throw new BadRequestException("Do not have permission");

        try {
            const limit = fetchMemberDto.limit || 50;
            const offset = limit * (fetchMemberDto.skip || 0);
            const { positionId, departmentId, deleted, endDate, startDate, hasOpportunity } = fetchMemberDto;

            const posArr = positionId
                ? positionId.split(",").map((element) => decodeURIComponent(element)?.trim())
                : [];
            const query: any = {
                company: companyId,
                ...(permissions === PermissionsEnum.Managed
                    ? { $or: [{ managerId: memberId }, { _id: memberId }] }
                    : permissions === PermissionsEnum.Self
                    ? { _id: memberId }
                    : {}),
                ...(departmentId ? { departmentId } : {}),
            };

            if (startDate && endDate) {
                query["hireDate"] = { $lte: endDate };
                query["$or"] = [
                    { deleted: false },
                    {
                        deleted: true,
                        terminateDate: {
                            $exists: true,
                            $gte: startDate,
                        },
                    },
                ];
            } else if (deleted === undefined) {
                query["$or"] = [
                    { deleted: false },
                    {
                        deleted: true,
                        terminateDate: { $exists: true },
                    },
                ];
            } else {
                query["deleted"] = deleted;
            }

            const company = await this.companyModel.findOne({ _id: companyId }).exec();
            if (!company) throw new HttpException("Company does not exist", HttpStatus.BAD_REQUEST);

            // Fetch stages for the company
            const stages = await this.crmStageModel
                .find({
                    companyId,
                    deleted: false,
                    stageGroup: StageGroupEnum.Sales,
                })
                .select("_id");

            const stageIds = stages.map((stage) => stage._id);

            const pipeline: any[] = [
                { $match: query },
                {
                    $lookup: {
                        from: "Compensation",
                        pipeline: [
                            {
                                $match: {
                                    ...(posArr && posArr.length ? { positionId: { $in: posArr } } : {}),
                                },
                            },
                        ],
                        localField: "_id",
                        foreignField: "memberId",
                        as: "result",
                    },
                },
                {
                    $unwind: {
                        path: "$result",
                        preserveNullAndEmptyArrays: false,
                    },
                },
            ];

            // Conditionally push opportunity lookup stages
            if (hasOpportunity) {
                pipeline.push(
                    {
                        $lookup: {
                            from: "Opportunity",
                            let: { memberId: "$_id" },
                            pipeline: [
                                {
                                    $match: {
                                        $expr: {
                                            $or: [
                                                { $eq: ["$salesPerson", "$$memberId"] },
                                                { $eq: ["$projectManager", "$$memberId"] },
                                            ],
                                        },
                                        companyId,
                                        deleted: { $ne: true },
                                        status: OpportunityStatusEnum.Active,
                                        stage: { $in: stageIds },
                                        nextAction: { $exists: true },
                                    },
                                },
                                { $sort: { "nextAction.due": 1 } },
                                { $limit: 1 },
                            ],
                            as: "opportunities",
                        },
                    },
                    {
                        $addFields: {
                            hasOpportunity: { $gt: [{ $size: "$opportunities" }, 0] },
                        },
                    },
                    {
                        $match: {
                            hasOpportunity: true,
                        },
                    },
                );
            }

            // Continue with the rest of the pipeline
            pipeline.push(
                {
                    $addFields: {
                        name: {
                            $cond: {
                                if: {
                                    $and: [
                                        { $ifNull: ["$preferredName", false] },
                                        { $ne: ["$preferredName", ""] },
                                    ],
                                },
                                then: {
                                    $concat: [
                                        "$preferredName",
                                        " ",
                                        { $arrayElemAt: [{ $split: ["$name", " "] }, 1] },
                                    ],
                                },
                                else: "$name",
                            },
                        },
                    },
                },
                {
                    $project: {
                        opportunities: 0,
                        result: 0,
                        hasOpportunity: 0,
                    },
                },
                { $sort: { createdAt: -1 } },
                { $skip: offset },
                { $limit: limit },
            );

            const members = await this.memberModel.aggregate(pipeline);
            return new OkResponse({ company, memberData: members });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getCompanyMembersByPositionWithActions(
        memberId: string,
        companyId: string,
        permissions: PermissionsEnum,
        fetchMemberDto: FetchMemberDto,
    ) {
        if (permissions === PermissionsEnum.None) throw new BadRequestException("Do not have permission");
        try {
            const limit = fetchMemberDto.limit || 50;
            const offset = limit * (fetchMemberDto.skip || 0);
            const { departmentId, deleted, endDate, startDate, hasOpportunity } = fetchMemberDto;
            const query: any = {
                company: companyId,
                ...(permissions === PermissionsEnum.Managed
                    ? { $or: [{ managerId: memberId }, { _id: memberId }] }
                    : permissions === PermissionsEnum.Self
                    ? { _id: memberId }
                    : {}),
                ...(departmentId ? { departmentId } : {}),
            };

            if (startDate && endDate) {
                query["hireDate"] = { $lte: endDate };
                query["$or"] = [
                    { deleted: false },
                    {
                        deleted: true,
                        terminateDate: {
                            $exists: true,
                            $gte: startDate,
                        },
                    },
                ];
            } else if (deleted === undefined) {
                query["$or"] = [
                    { deleted: false },
                    {
                        deleted: true,
                        terminateDate: { $exists: true },
                    },
                ];
            } else {
                query["deleted"] = deleted;
            }

            const company = await this.companyModel.findOne({ _id: companyId }).exec();
            if (!company) throw new HttpException("Company does not exist", HttpStatus.BAD_REQUEST);

            // Fetch stages for the company
            const stages = await this.crmStageModel
                .find({
                    companyId,
                    deleted: false,
                    stageGroup: StageGroupEnum.Sales,
                })
                .select("_id");

            const stageIds = stages.map((stage) => stage._id);
            const pipeline: any[] = [
                { $match: query },
                {
                    $lookup: {
                        from: "Compensation",

                        localField: "_id",
                        foreignField: "memberId",
                        as: "result",
                    },
                },
                {
                    $unwind: {
                        path: "$result",
                        preserveNullAndEmptyArrays: false,
                    },
                },
                {
                    $lookup: {
                        from: "Position",
                        let: { posId: "$result.positionId" },
                        pipeline: [
                            {
                                $match: {
                                    $expr: { $eq: ["$_id", "$$posId"] },
                                },
                            },
                            {
                                $match: {
                                    permissions: {
                                        $elemMatch: {
                                            resources: {
                                                $elemMatch: {
                                                    name: "actions",
                                                    permissions: { $lte: 3 },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        ],
                        as: "positionMatch",
                    },
                },
                {
                    $match: {
                        positionMatch: { $ne: [] },
                    },
                },
            ];

            // Conditionally push opportunity lookup stages
            if (hasOpportunity) {
                pipeline.push(
                    {
                        $lookup: {
                            from: "Opportunity",
                            let: { memberId: "$_id" },
                            pipeline: [
                                {
                                    $match: {
                                        $expr: {
                                            $or: [
                                                { $eq: ["$salesPerson", "$$memberId"] },
                                                { $eq: ["$projectManager", "$$memberId"] },
                                            ],
                                        },
                                        companyId,
                                        deleted: { $ne: true },
                                        status: OpportunityStatusEnum.Active,
                                        stage: { $in: stageIds },
                                        nextAction: { $exists: true },
                                    },
                                },
                                { $sort: { "nextAction.due": 1 } },
                                { $limit: 1 },
                            ],
                            as: "opportunities",
                        },
                    },
                    {
                        $addFields: {
                            hasOpportunity: { $gt: [{ $size: "$opportunities" }, 0] },
                        },
                    },
                    {
                        $match: {
                            hasOpportunity: true,
                        },
                    },
                );
            }
            pipeline.push(
                {
                    $addFields: {
                        name: {
                            $cond: {
                                if: {
                                    $and: [
                                        { $ifNull: ["$preferredName", false] },
                                        { $ne: ["$preferredName", ""] },
                                    ],
                                },
                                then: {
                                    $concat: [
                                        "$preferredName",
                                        " ",
                                        { $arrayElemAt: [{ $split: ["$name", " "] }, 1] },
                                    ],
                                },
                                else: "$name",
                            },
                        },
                    },
                },
                {
                    $project: {
                        opportunities: 0,
                        result: 0,
                        hasOpportunity: 0,
                    },
                },
                { $sort: { createdAt: -1 } },
                { $skip: offset },
                { $limit: limit },
            );

            const members = await this.memberModel.aggregate(pipeline);

            return new OkResponse({ company, memberData: members });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    //TODO: api added for chanse watson to get all sales persons & PM's
    async getSalesPersonAndProjectManager(companyId: string, departmentId: string, onlyPM: boolean) {
        try {
            const query: any = {
                company: companyId,
                deleted: false,
            };
            if (departmentId) query["departmentId"] = departmentId;
            const symbolArr = onlyPM
                ? ["ProjectManager", "GeneralManager"]
                : ["SalesPerson", "ProjectManager", "RRTech", "GeneralManager", "SalesManager"];

            const members = await this.memberModel.aggregate([
                {
                    $match: query,
                },
                {
                    $lookup: {
                        from: "Compensation",
                        localField: "_id",
                        foreignField: "memberId",
                        as: "comp",
                        pipeline: [
                            {
                                $project: {
                                    positionId: 1,
                                },
                            },
                        ],
                    },
                },
                {
                    $lookup: {
                        from: "Position",
                        localField: "comp.positionId",
                        foreignField: "_id",
                        as: "position",
                        pipeline: [
                            {
                                $match: {
                                    ...(!departmentId ? { symbol: { $in: symbolArr } } : {}),
                                },
                            },
                        ],
                    },
                },
                {
                    $unwind: {
                        path: "$position",
                        preserveNullAndEmptyArrays: false,
                    },
                },
                {
                    $addFields: {
                        name: {
                            $cond: {
                                if: {
                                    $and: [
                                        { $ifNull: ["$preferredName", false] },
                                        { $ne: ["$preferredName", ""] },
                                    ],
                                },
                                then: {
                                    $concat: [
                                        "$preferredName",
                                        " ",
                                        {
                                            $arrayElemAt: [{ $split: ["$name", " "] }, 1],
                                        },
                                    ],
                                },
                                else: "$name",
                            },
                        },
                    },
                },
                { $sort: { createdAt: -1 } },
                {
                    $project: {
                        name: 1,
                    },
                },
            ]);
            return new OkResponse({ members });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getCompanyInvitations(
        userId: string,
        companyId: string,
        paginationRequestDto: PaginationRequestDto,
    ) {
        try {
            const limit = paginationRequestDto.limit || 10;
            const offset = limit * (paginationRequestDto.skip || 0);
            const company = await this.companyModel.findOne({ _id: companyId }).exec();
            if (!company) throw new HttpException("Company does not exist", HttpStatus.BAD_REQUEST);
            const [invitations, totalItems] = await Promise.all([
                this.invitationModel
                    .find({ company: { $in: company._id } })
                    .sort({ createdAt: -1 })
                    .skip(offset)
                    .limit(limit)
                    .exec(),
                this.invitationModel.countDocuments({ company: { $in: company._id } }).exec(),
            ]);

            return new OkResponse({ invitations, totalItems });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // async getUserInvitations(companyId: string, userInvitationDto: UserInvitationDto) {
    //     try {
    //         const { email, status } = userInvitationDto;
    //         const value =
    //             companyId && status
    //                 ? { email, company: companyId, status }
    //                 : companyId
    //                 ? { email, company: companyId }
    //                 : status
    //                 ? { email, status }
    //                 : { email };
    //         const invitation = await this.invitationModel.find(value);
    //         return invitation;
    //     } catch (error: any) {
    //         if (error instanceof HttpException) {
    //             throw error;
    //         }
    //         throw new InternalServerErrorException(error.message);
    //     }
    // }

    async getUserInvitationById(id: string) {
        try {
            const invitation: any = await this.invitationModel.findOne({ _id: id });
            const companyRecord = await this.companyModel.findOne(
                { _id: invitation.company },
                { companyName: 1, _id: 0 },
            );

            const invObject = { ...invitation, companyName: companyRecord.companyName };
            return new OkResponse({ invitation: invObject });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getDirectReportsByManagerId(userId: string, companyId: string, memberId: string) {
        try {
            const directReports = await this.memberModel.aggregate([
                {
                    $match: {
                        managerId: memberId,
                        company: companyId,
                        deleted: false,
                    },
                },
                {
                    $lookup: {
                        from: "User",
                        localField: "user",
                        foreignField: "_id",
                        as: "result",
                    },
                },
                {
                    $unwind: {
                        path: "$result",
                    },
                },
                {
                    $project: {
                        memberId: "$_id",
                        userId: "$user",
                        managerId: "$managerId",
                        email: "$email",
                        name: {
                            $concat: [
                                "$result.firstName",
                                {
                                    $cond: {
                                        if: { $ifNull: ["$result.lastName", false] },
                                        then: { $concat: [" ", "$result.lastName"] },
                                        else: "",
                                    },
                                },
                            ],
                        },
                    },
                },
            ]);

            return new OkResponse({ directReports });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async terminateMember(
        userId: string,
        companyId: string,
        terminateMemberDto: TerminateMemberDto,
        teamPermission: number,
    ) {
        try {
            const member = await this.memberModel
                .findOne({
                    _id: terminateMemberDto.memberId,
                    deleted: false,
                })
                .exec();
            if (!member) throw new HttpException("Member does not exists", HttpStatus.BAD_REQUEST);
            const isCrewManager = await this.crewModel.findOne({
                managerId: terminateMemberDto.memberId,
                deleted: false,
            });
            const isCrewMember: any = await this.crewMemberModel
                .findOne({
                    memberId: terminateMemberDto.memberId,
                    deleted: false,
                })
                .populate("crewId", "name", "Crew");

            // throw new HttpException(
            //     `Remove this member from ${isCrewManager?.name ?? "Crew"} before terminating`,
            //     HttpStatus.BAD_REQUEST,
            // );

            if (teamPermission === PermissionsEnum.Managed) {
                const manager = await this.getMemberByUserId(userId, companyId);
                if (manager.data.member._id !== member.managerId)
                    throw new HttpException(
                        "You are unauthorized to perform this request",
                        HttpStatus.BAD_REQUEST,
                    );
            }
            let message = `${member.name} terminated successfully`;

            // if part of crew member then directly deleting the crew meber
            if (isCrewManager) {
                await this.crewModel.updateOne(
                    { _id: isCrewManager._id },
                    {
                        $unset: {
                            managerId: 1,
                        },
                    },
                );
                message = message + ` and removed as manager of ${isCrewManager.name}`;
            }
            if (isCrewMember) {
                await this.crewMemberModel.updateOne(
                    { _id: isCrewMember._id },
                    {
                        $set: {
                            deleted: true,
                            removeDate: terminateMemberDto.terminateDate,
                        },
                    },
                );
                message = message + ` and removed from ${isCrewMember?.crewId?.name} on $Date`;
            }

            const result = await this.memberModel.updateOne(
                { _id: terminateMemberDto.memberId },
                {
                    $set: {
                        deleted: true,
                        message: terminateMemberDto.message,
                        terminateDate: terminateMemberDto.terminateDate,
                    },
                },
            );
            if (result.modifiedCount === 0) {
                throw new BadRequestException({ message: "Failed to update changes!" });
            }
            // update company analatyics
            this.companyAnalyticsService.updateCompanyTeamAnalytics(companyId, { terminated: 1 });

            return new NoContentResponse({ message, date: terminateMemberDto.terminateDate });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restoreMember(
        userId: string,
        getCompanyMemberDto: GetCompanyMemberDto,
        teamPermission: number,
        companyId: string,
    ) {
        try {
            const [member, company, activeMemberCount] = await Promise.all([
                this.memberModel
                    .findOne({
                        _id: getCompanyMemberDto.memberId,
                        deleted: true,
                    })
                    .exec(),
                this.companyModel
                    .findOne({ _id: companyId })
                    .select("_id planType subscribedTeamSize")
                    .exec(),
                this.memberModel.countDocuments({
                    company: companyId,
                    deleted: false,
                }),
            ]);

            if (!member) throw new HttpException("Member does not exists", HttpStatus.BAD_REQUEST);

            if (!company) throw new HttpException("Company does not exist", HttpStatus.BAD_REQUEST);
            if (company.planType === SubscriptionPlanTypeEnum.FREE)
                throw new HttpException(
                    "Subscribe to a Plan for inviting people",
                    HttpStatus.METHOD_NOT_ALLOWED,
                );
            if (activeMemberCount >= company.subscribedTeamSize)
                throw new HttpException(
                    "Team size limit exceeded. Unable to add more members.",
                    HttpStatus.METHOD_NOT_ALLOWED,
                );

            const diffTime = Math.abs(new Date().getTime() - member?.terminateDate?.getTime()); // Get difference in milliseconds
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)); // Convert to days

            if (diffDays < 10)
                throw new HttpException(
                    `Member restoration is restricted. A minimum of 10 days is required after termination. (${
                        10 - diffDays
                    } days left)`,
                    HttpStatus.METHOD_NOT_ALLOWED,
                );

            if (teamPermission === PermissionsEnum.Managed) {
                const manager = await this.getMemberByUserId(userId, companyId);
                if (manager.data.member._id !== member.managerId)
                    throw new HttpException(
                        "You are unauthorized to perform this request",
                        HttpStatus.BAD_REQUEST,
                    );
            }
            const result = await this.memberModel.updateOne(
                { _id: getCompanyMemberDto.memberId },
                {
                    $set: {
                        deleted: false,
                    },
                    $unset: {
                        terminateDate: 1,
                        message: 1,
                    },
                },
            );
            if (result.modifiedCount === 0) {
                throw new BadRequestException({ message: "Failed to update changes!" });
            }

            // update company analatyics
            this.companyAnalyticsService.updateCompanyTeamAnalytics(companyId, {
                restored: 1,
            });

            return new NoContentResponse({ message: "Member Restored successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async permDeleteMember(
        userId: string,
        companyId: string,
        getCompanyMemberDto: GetCompanyMemberDto,
        teamPermission: number,
    ) {
        try {
            const member = await this.memberModel
                .findOne({
                    _id: getCompanyMemberDto.memberId,
                    deleted: true,
                })
                .exec();
            if (!member) throw new HttpException("Member does not exist", HttpStatus.BAD_REQUEST);
            const isCrewManager = await this.crewModel.findOne({
                managerId: getCompanyMemberDto.memberId,
                deleted: false,
            });
            const isCrewMember = await this.crewMemberModel.findOne({
                memberId: getCompanyMemberDto.memberId,
                deleted: false,
            });
            if (isCrewManager || isCrewMember)
                throw new HttpException(
                    `Remove this member from ${isCrewManager?.name ?? "Crew"} before deleting`,
                    HttpStatus.BAD_REQUEST,
                );
            if (teamPermission === PermissionsEnum.Managed) {
                const manager = await this.getMemberByUserId(userId, companyId);
                if (manager.data.member._id !== member.managerId)
                    throw new HttpException(
                        "You are unauthorized to perform this request",
                        HttpStatus.BAD_REQUEST,
                    );
            }
            await this.memberModel.deleteOne({ _id: getCompanyMemberDto.memberId });
            return new NoContentResponse({ message: "Member deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getMemberDetail(userId: string, companyId: string, memberId: string, teamPermission: number) {
        try {
            const company = await this.companyModel
                .findOne({ _id: companyId, deleted: { $ne: true } })
                .exec();
            if (!company) throw new NotFoundException("Company does not exist");
            let memberInfo: any = {};
            const member: any = await this.memberModel.aggregate([
                {
                    $match: {
                        company: company._id,
                        deleted: false,
                        _id: memberId,
                    },
                },
                {
                    $lookup: {
                        from: "Department",
                        localField: "departmentId",
                        foreignField: "_id",
                        as: "departmentDetail",
                    },
                },
            ]);
            await this.positionService.permissionCheck(
                userId,
                companyId,
                memberId, //not used in case of team permission check
                member[0]?.managerId,
                teamPermission,
            );
            const manager = await this.memberModel.findOne({ _id: member[0].managerId });
            const managerDetail = manager ? await this.userModel.findOne({ _id: manager.user }) : undefined;
            memberInfo = { ...member[0] };
            memberInfo = {
                managerName: managerDetail
                    ? managerDetail.firstName + (managerDetail?.lastName ? " " + managerDetail.lastName : "")
                    : undefined,
                ...memberInfo,
            };
            return new OkResponse({ memberInfo });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getMemberByUserId(userId: string, companyId: string) {
        try {
            const company = await this.companyModel
                .findOne({ _id: companyId, deleted: { $ne: true } })
                .exec();
            if (!company) throw new NotFoundException("Company does not exist");
            const member = await this.memberModel.findOne({
                company: company._id,
                user: userId,
                deleted: false,
            });
            return new OkResponse({ member });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getMemberByMemberIdInternal(memberId: string, companyId: string) {
        try {
            return await this.memberModel.findOne({
                _id: memberId,
                company: companyId,
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getUserByMemberId(memberId: string, companyId: string) {
        try {
            const member = await this.memberModel.findOne({
                company: companyId,
                _id: memberId,
                deleted: false,
            });
            const user = await this.userModel.findOne({
                _id: member.user,
            });
            return user;
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getUserByEmail(email: string) {
        try {
            const user = await this.userService.getUserByUsernameOrEmailOrToken(email);
            if (user) return new OkResponse({ message: "The user has already been registered!" });
            else return new OkResponse({ message: "User does not exist!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async upsertCompanySetting(companyId: string, createCompanySettingDto: CreateCompanySettingDto) {
        try {
            const modOH = roundTo3(createCompanySettingDto.dailyOH / createCompanySettingDto.dailyLabor);
            const modP = roundTo3(createCompanySettingDto.dailyProfit / createCompanySettingDto.dailyLabor);
            const ttlBurden =
                createCompanySettingDto.insWorkersComp +
                createCompanySettingDto.insUnemployment +
                createCompanySettingDto.ssMedicare;

            const modTtl = (1 + ttlBurden + modOH + modP) / (1 - createCompanySettingDto.salesComm);
            const modS = modTtl * createCompanySettingDto.salesComm;

            await this.companySettingModel.updateOne(
                {
                    _id: createCompanySettingDto._id,
                    companyId,
                    deleted: false,
                },
                {
                    $set: {
                        ...createCompanySettingDto,
                        modOH,
                        modP,
                        modS,
                        modTtl,
                        ttlBurden,
                    },
                },
                { upsert: true },
            );
            return new CreatedResponse({ message: "Company Setting upserted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getCompanySetting(userId: string, companyId: string) {
        try {
            const companySetting = await this.companySettingModel.findOne({ companyId, deleted: false });
            return new OkResponse({ companySetting });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getCompanyAddress(userId: string, companyId: string) {
        try {
            const companySetting = await this.companySettingModel
                .findOne({ companyId, deleted: false })
                .select(
                    "_id companyId defaultPO address latitude longitude weekStartDay weekEndDays workingStates gpsEnable gpsTimeInterval email phone",
                );
            return new OkResponse({ companySetting });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getManagerByMemberId(memberId: string) {
        try {
            const member = await this.memberModel.findOne({
                _id: memberId,
                deleted: false,
            });
            return new OkResponse({ member: member.managerId });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async upsertCompanyCommision(companyId: string, createCompanyCommisionDto: CreateCompanyCommisionDto) {
        try {
            const { createdBy, benchmarks, typeBonus, salesPositions, pieceWorkPositions, versions } =
                createCompanyCommisionDto;

            const dataToBeUpdated = {
                companyId,
                createdBy,
                sales: {
                    benchmarks,
                    typeBonus,
                    positions: salesPositions,
                },
                pieceWork: {
                    positions: pieceWorkPositions,
                    versions,
                },
            };

            await this.companyPayModel.updateOne(
                {
                    _id: createCompanyCommisionDto._id,
                    companyId,
                },
                {
                    $set: {
                        ...dataToBeUpdated,
                    },
                },
                { upsert: true },
            );
            return new CreatedResponse({ message: "Company Commision Setting updated successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getCompanyCommisionSettings(userId: string, companyId: string) {
        try {
            const companyCommision = await this.companyPayModel.findOne({
                companyId,
            });
            return new OkResponse({ companyCommision });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createReferrer(companyId: string, createReferrer: CreateReferrerDto) {
        const referrer = await this.referrersModel.create({ companyId, ...createReferrer });

        return new CreatedResponse({ data: referrer, message: "New Referrer added successfully!" });
    }

    async getReferrers(
        user,
        companyId: string,
        deleted: boolean,
        allReferrers: boolean,
        search: string,
        referredBy: string,
    ) {
        const { data } = await this.contactService.searchContacts(
            {
                ...(search && search !== "" && { search }),
                limit: 50,
                skip: 1,
                fields: JSON.stringify({ _id: 1, fullName: 1, isBusiness: 1, businessName: 1 }),
            },
            { _id: user, companyId },
        );

        let referrers = data.contacts.map((c) => ({
            _id: c._id,
            name: c.isBusiness
                ? `${c.businessName ? c.businessName : ""}${c.fullName ? ` (${c.fullName})` : ""}`
                : c.fullName,
        }));

        // to add referredBy contact if not in the list
        if (referredBy && referredBy !== "" && !referrers.find((c) => c._id === referredBy)) {
            const referredByContact = await this.contactService.findOneWithLimitedData(
                referredBy,
                { companyId },
                { fullName: 1, isBusiness: 1, businessName: 1 },
            );
            if (referredByContact?.fullName) {
                referrers.push({
                    _id: referredByContact._id,
                    name: referredByContact.isBusiness
                        ? `${referredByContact.businessName ? referredByContact.businessName : ""}${
                              referredByContact.fullName ? ` (${referredByContact.fullName})` : ""
                          }`
                        : referredByContact.fullName,
                });
            }
        }

        if (allReferrers) {
            const member: any = await this.memberModel
                .find({ company: companyId, deleted: false })
                .select("_id name");

            referrers = referrers.concat(member).sort((a, b) => a.name.localeCompare(b.name));
        }
        // console.log("referrers", referrers);

        return new OkResponse({ referrers });
    }

    async restoreReferrer(companyId: string, restoreReferrer: DeleteRestoreReferrerDto) {
        const { id } = restoreReferrer;
        const result = await this.referrersModel.updateOne(
            { _id: id, companyId, deleted: true },
            { $set: { deleted: false } },
        );

        if (result.modifiedCount === 0) {
            throw new BadRequestException({ message: "Failed to update changes!" });
        }
        return new NoContentResponse({ message: "Referrer Restored successfully!" });
    }

    async deleteReferrer(companyId: string, deleteReferrer: DeleteRestoreReferrerDto) {
        const { id } = deleteReferrer;
        const result = await this.referrersModel.updateOne(
            { _id: id, companyId, deleted: false },
            { $set: { deleted: true } },
        );

        if (result.modifiedCount === 0) {
            throw new BadRequestException({ message: "Failed to update changes!" });
        }
        return new NoContentResponse({ message: "Referrer deleted successfully!" });
    }

    async permDeleteReferrer(companyId: string, deleteReferrer: DeleteRestoreReferrerDto) {
        const { id } = deleteReferrer;
        await this.referrersModel.deleteOne({ _id: id, companyId });

        return new OkResponse({ message: "Referrer deleted successfully!" });
    }

    async weatherApi(zipcode: string, date: Date) {
        try {
            const url = `https://api.weatherapi.com/v1/history.json?q=${zipcode}&dt=${date.toISOString()}&key=${
                process.env.WEATHER_APIKEY
            }`;

            const response = await fetch(url);

            if (!response.ok) {
                throw new Error("Network Error");
            }
            const data = await response.json();
            return new OkResponse({ weather: data });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createContract(companyId: string, createContractDto: CreateContractDto) {
        // check for duplicate name
        const id = randomUUID();
        await this.checkDuplicateContract(
            id,
            createContractDto.contractName,
            createContractDto.projectType,
            companyId,
        );

        let session;
        try {
            // Starting session for Mongo transaction
            session = await this.connection.startSession();
            session.startTransaction();

            const { projectType, isDefault } = createContractDto;

            if (createContractDto.sections.length) {
                createContractDto.sections.forEach((section) => {
                    section["_id"] = randomUUID();
                });
            }

            const contract = new this.contractModel({ _id: id, companyId, ...createContractDto });
            await contract.save({ session });
            if (isDefault) {
                await this.contractModel.updateMany(
                    {
                        _id: { $ne: id },
                        companyId,
                        projectType,
                        isDefault: true,
                    },
                    { $set: { isDefault: false } },
                    { session },
                );
            }

            // ending session of mongodb
            await session.commitTransaction();
            session.endSession();

            return new OkResponse({ message: "Contract created successfully" });
        } catch (error) {
            await session.abortTransaction();
            session.endSession();
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async checkDuplicateContract(id: string, contractName: string, projectType: string, companyId: string) {
        try {
            const contracts = await this.contractModel
                .find({ _id: { $ne: id }, contractName, companyId, projectType, deleted: false })
                .exec();

            if (contracts.length) throw new ConflictException("Contract with same name already exists!");
            else return true;
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async findAllContracts(companyId: string, projectType: string[]) {
        try {
            const query = {
                companyId,
                deleted: false,
            };
            if (projectType.length) {
                query["projectType"] = { $in: projectType };
            }
            const contracts = await this.contractModel.find(query).exec();
            return new OkResponse({ contracts });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async findContract(id: string, companyId: string) {
        try {
            const contract = await this.contractModel.findOne({ _id: id, companyId, deleted: false }).exec();
            if (!contract) {
                throw new NotFoundException(`Contract with id not found`);
            }
            return new OkResponse({ contract });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateContract(id: string, companyId: string, updateContractDto: UpdateContractDto) {
        // check for duplicate name
        await this.checkDuplicateContract(
            id,
            updateContractDto.contractName,
            updateContractDto.projectType,
            companyId,
        );

        let session;
        try {
            // Starting session for Mongo transaction
            session = await this.connection.startSession();
            session.startTransaction();

            const updatedContract = await this.contractModel
                .findOneAndUpdate({ _id: id, companyId }, updateContractDto, { new: true, session })
                .exec();
            if (updateContractDto.isDefault) {
                await this.contractModel.updateMany(
                    {
                        _id: { $ne: id },
                        companyId,
                        projectType: updatedContract.projectType,
                        isDefault: true,
                    },
                    { $set: { isDefault: false } },
                    { session },
                );
            }

            // ending session of mongodb
            await session.commitTransaction();
            session.endSession();

            if (!updatedContract) {
                throw new NotFoundException(`Contract not found`);
            }

            return new OkResponse({ message: "Contract update succesfully" });
        } catch (error) {
            await session.abortTransaction();
            session.endSession();
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteContract(id: string, companyId: string) {
        try {
            const result = await this.contractModel
                .findOneAndUpdate({ _id: id, companyId }, { deleted: true })
                .exec();

            if (!result) {
                throw new NotFoundException(`Contract with not found`);
            }
            return new OkResponse({ message: "Contract deleted succesfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteSection(sectionId: string, contractId: string, companyId: string) {
        try {
            const result = await this.contractModel
                .findOneAndUpdate(
                    { _id: contractId, companyId, "sections._id": sectionId, deleted: false },
                    { $pull: { sections: { _id: sectionId } } },
                    { new: true },
                )
                .exec();

            if (!result) {
                throw new NotFoundException(`Section Not found`);
            }

            return new OkResponse({ message: "Section deleted successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addSection(companyId: string, contractId: string, createSectionDto: CreateSectionDto) {
        try {
            await this.contractModel.updateOne(
                { _id: contractId, companyId },
                { $push: { sections: { _id: randomUUID(), ...createSectionDto } } },
            );
            return new OkResponse({ message: "Section added successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateSection(
        companyId: string,
        contractId: string,
        sectionId: string,
        updateSectionDto: UpdateSectionDto,
    ) {
        try {
            const result = await this.contractModel.findOneAndUpdate(
                {
                    _id: contractId,
                    companyId,
                    deleted: false,
                    "sections._id": sectionId, // Ensure the section exists
                },
                {
                    $set: {
                        "sections.$": { _id: sectionId, ...updateSectionDto }, // Update the matching section
                    },
                },
                { new: true }, // Return the updated document
            );

            if (!result) {
                throw new NotFoundException("Contract or section not found");
            }

            return new OkResponse({ message: "Section updated successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getSections(companyId: string, contractId: string) {
        try {
            const contract = await this.contractModel.findOne(
                {
                    _id: contractId,
                    companyId,
                    deleted: false,
                },
                { sections: 1 },
            );

            if (!contract) {
                throw new NotFoundException("Contract not found");
            }

            return new OkResponse({ sections: contract.sections });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateSectionOrder(companyId: string, contractId: string, updateSectionDto: UpdateSectionOrderDto) {
        try {
            const contractData = await this.contractModel.findOne(
                { _id: contractId, companyId, deleted: false },
                { sections: 1 },
            );

            if (!contractData) {
                throw new NotFoundException("Contract not found");
            }
            const sectionOrderMapping = updateSectionDto.sections.reduce((acc, section) => {
                acc[section.sectionId] = section.order;
                return acc;
            }, {});

            const newUpdatedSection = contractData.sections.map((section) => {
                return {
                    ...section,
                    order: sectionOrderMapping[section._id] || section.order, // Update the order
                };
            });

            await this.contractModel.updateOne(
                { _id: contractId, companyId },
                { $set: { sections: newUpdatedSection } },
            );

            return new OkResponse({ message: "Section order updated successfully" });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createDefaultMediaSetting(companyId: string, createMediaSettingDto: CreateMediaSettingDto) {
        try {
            await this.mediaSettingModel.create({
                companyId,
                ...createMediaSettingDto,
            });

            return new OkResponse({
                message: "Media setting document created successfully!",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getMediaSettings(companyId: string) {
        try {
            const mediaSetting = await this.mediaSettingModel.findOne({ companyId }).exec();

            return new OkResponse({
                mediaSetting,
                message: "Media setting retrieved successfully!",
            });
        } catch (error: any) {
            throw new InternalServerErrorException(error.message);
        }
    }

    async getTagsList(companyId: string) {
        try {
            const tagDoc = await this.mediaSettingModel.findOne({ companyId }).select("tags");

            return new OkResponse({
                tags: tagDoc ? tagDoc.tags : [],
                message: "Tags retrieved successfully!",
            });
        } catch (error: any) {
            throw new InternalServerErrorException(error.message);
        }
    }

    async addTagsToList(companyId: string, tagArr: string[]) {
        try {
            const result = await this.mediaSettingModel.findOne({ companyId }).select("tags");

            // if not present then create new document
            if (!result) {
                await this.updateMediaSetting(companyId, { tags: tagArr });
                return new OkResponse({
                    message: "Tags added successfully!",
                });
            }

            // Filter tags that are already present
            const newTags = tagArr
                .filter((tag) => typeof tag === "string")
                .map((tag) => tag.trim())
                .filter((tag) => tag !== "")
                .filter((tag) => !result.tags.includes(tag));

            // Check if adding new tags would exceed the limit
            if (result.tags.length + newTags.length > 100) {
                throw new BadRequestException("Limit reached, Tags array cannot exceed 100 items");
            }

            if (newTags.length > 0)
                await this.mediaSettingModel.updateOne(
                    { companyId },
                    { $push: { tags: { $each: newTags } } },
                );

            return new OkResponse({
                message: "Tags added successfully!",
            });
        } catch (error: any) {
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateMediaSetting(companyId: string, updateTagDto: UpdateTagDto) {
        try {
            if (updateTagDto.tags.length >= 100)
                throw new BadRequestException("Limit reached, Tags array cannot exceed 100 items");

            if (updateTagDto.tags) {
                const tagSet = new Set();

                for (const tag of updateTagDto.tags) {
                    if (tagSet.has(tag)) {
                        throw new HttpException("Tags must be unique", HttpStatus.BAD_REQUEST);
                    }
                    tagSet.add(tag);
                }
            }

            const updatedTag = await this.mediaSettingModel.findOneAndUpdate(
                { companyId },
                { $set: { ...updateTagDto } },
                { upsert: true, new: true },
            );
            if (!updatedTag) {
                throw new HttpException("Media Setting document not found", HttpStatus.NOT_FOUND);
            }

            return new OkResponse({
                tag: updatedTag,
                message: "Media setting updated successfully!",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteTag(companyId: string, deleteTagDto: DeleteTagDto) {
        try {
            const updatedTag = await this.mediaSettingModel
                .findOneAndUpdate({ companyId }, { $pull: { tags: deleteTagDto.tag } }, { new: true })
                .exec();

            if (!updatedTag) {
                throw new HttpException("Tag document not found or tag not present", HttpStatus.NOT_FOUND);
            }

            return new OkResponse({
                tag: updatedTag,
                message: "Tag deleted successfully!",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async addAction(companyId: string, createSalesActionDto: CreateSalesActionCompanyDto) {
        try {
            const result = await this.salesActionModel.updateOne(
                { companyId, memberId: companyId },
                {
                    $push: { actions: createSalesActionDto.action },
                },
            );

            if (result.modifiedCount === 0) {
                throw new NotFoundException("SalesAction record not found for this companyId");
            }

            return new OkResponse({
                message: "Action added successfully!",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateSalesAction(companyId: string, updateSalesActionDto: UpdateSalesActionDto) {
        try {
            const result = await this.salesActionModel.updateOne(
                { companyId, memberId: companyId, "actions._id": updateSalesActionDto.actionId },
                {
                    $set: {
                        "actions.$.name": updateSalesActionDto.name,
                        "actions.$.type": updateSalesActionDto.type,
                    },
                },
            );

            if (result.modifiedCount === 0) {
                throw new NotFoundException("Action not found for the given companyId and actionId");
            }

            return new OkResponse({
                message: "Action updated successfully!",
            });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteSalesAction(companyId: string, deleteSalesActionDto: DeleteSalesActionDto) {
        try {
            const { actionId } = deleteSalesActionDto;

            const existingSalesAction = await this.salesActionModel.findOne({
                "actions._id": actionId,
                companyId,
                memberId: companyId,
                deleted: false,
            });

            if (!existingSalesAction) {
                throw new HttpException("Sales action not found or already deleted!", HttpStatus.NOT_FOUND);
            }

            const result = await this.salesActionModel.updateOne(
                { "actions._id": actionId, companyId, deleted: false },
                { $pull: { actions: { _id: actionId } } },
            );

            if (result.modifiedCount === 0) {
                throw new HttpException("Failed to delete action!", HttpStatus.BAD_REQUEST);
            }

            return new NoContentResponse({ message: "Sales action deleted successfully!" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getSalesActionOfCompanyDefault(userId: string, companyId: string) {
        try {
            const salesAction = await this.salesActionModel.findOne({
                memberId: companyId,
                companyId,
                deleted: false,
            });

            return new OkResponse({ salesAction });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async createContentBlock(companyId: string, memberId: string, contentBlockDto: CreateContentBlockDto) {
        try {
            // if (
            //     contentBlockDto.type === ContentBlockTypeEnum.CONTRACT &&
            //     (!contentBlockDto.state.length || !contentBlockDto.projectType.length)
            // )
            //     throw new BadRequestException("content for contract should have state & project type!");

            // if (contentBlockDto.type === ContentBlockTypeEnum.CONTRACT) {
            //     const contents = await this.contentBlockModel.exists({
            //         companyId,
            //         type: contentBlockDto.type,
            //         projectType: { $in: contentBlockDto.projectType },
            //         state: { $in: contentBlockDto.state },
            //         deleted: false,
            //     });
            //     if (contents)
            //         throw new BadRequestException("content for this project type & state already exists!");
            // }

            const result = new this.contentBlockModel({
                companyId,
                createdBy: memberId,
                ...contentBlockDto,
            });
            await result.save();

            return new OkResponse({ message: "New content block created" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async updateContentBlock(companyId: string, contentBlockDto: UpdateContentBlockDto) {
        try {
            const { modifiedCount } = await this.contentBlockModel.updateOne(
                { _id: contentBlockDto._id, companyId },
                {
                    $set: {
                        ...contentBlockDto,
                    },
                },
            );

            if (modifiedCount === 0) {
                throw new BadRequestException({ message: "Failed to update changes!" });
            }
            return new OkResponse({ message: "Content block updated" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getAllContentBlock(companyId: string, deleted: boolean, getContentDto: GetContentDto) {
        try {
            const { type, projectType, state } = getContentDto;
            const query = {
                ...(type && { type }),
                ...(projectType && { projectType: { $in: projectType } }),
                ...(state && { state: { $in: state } }),
            };

            const contents = await this.contentBlockModel.find({ companyId, deleted, ...query });

            return new OkResponse({ contents });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getContentBlockById(companyId: string, _id: string) {
        try {
            const content = await this.contentBlockModel.findOne({ _id, companyId });

            return new OkResponse({ content });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async deleteContentBlockById(companyId: string, _id: string) {
        try {
            const contractsInUse = await this.contractModel.find(
                {
                    companyId,
                    deleted: { $ne: true },
                    finePrint: { $exists: true },
                },
                { contractName: 1, finePrint: 1 },
            );

            const contractsUsingContentBlock = contractsInUse.filter((contract) =>
                Object.values(contract.finePrint).includes(_id),
            );

            if (contractsUsingContentBlock.length > 0) {
                const contractNames = contractsUsingContentBlock.map((c) => c.contractName).join(", ");
                throw new BadRequestException(
                    `This content block is being used in the following contracts: ${contractNames}`,
                );
            }

            const { modifiedCount } = await this.contentBlockModel.updateOne(
                { _id, companyId, deleted: false },
                { $set: { deleted: true } },
            );

            if (modifiedCount === 0) {
                throw new NotFoundException(`Content Not found`);
            }

            return new OkResponse({ message: "Content deleted successfully" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async restoreContentBlockById(companyId: string, _id: string) {
        try {
            const { modifiedCount } = await this.contentBlockModel.updateOne(
                { _id, companyId, deleted: true },
                { $set: { deleted: false } },
            );

            if (modifiedCount === 0) {
                throw new NotFoundException(`Content Not found`);
            }

            return new OkResponse({ message: "Content restored successfully" });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }
}
