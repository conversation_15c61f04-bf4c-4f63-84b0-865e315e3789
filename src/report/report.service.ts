/* eslint-disable prettier/prettier */
import { BadRequestException, HttpException, Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { WeekEnum } from "src/company/enum/week.enum";
import { CompanyPayDocument } from "src/company/schema/company-pay.schema";
import { CompanySettingDocument } from "src/company/schema/company-setting.schema";
import { MemberDocument } from "src/company/schema/member.schema";
import { WageIntervalEnum } from "src/compensation/enum/wage-interval.enum";
import { OpportunityStatusEnum } from "src/opportunity/enum/opportunityStatus.enum";
import { CompensationDocument } from "src/compensation/schema/compensation.schema";
import { CrewService } from "src/crew/crew.service";
import { CrewDocument } from "src/crew/schema/crew-management.schema";
import { StageGroupEnum } from "src/crm/enum/stage-group.enum";
import { CrmCheckpointDocument } from "src/crm/schema/crm-checkpoint.schema";
import { CrmStageDocument } from "src/crm/schema/crm-stage.schema";
import { CommissionModificationDocument } from "src/opportunity/schema/opp-commission.schema";
import { OpportunityDocument } from "src/opportunity/schema/opportunity.schema";
import { DailyLogDocument } from "src/daily-log/schema/daily-log.schema";
import { PeriodEnum } from "src/pay-schedule/enum/period.enum";
import { PayScheduleDocument } from "src/pay-schedule/schema/pay-schedule.schema";
import { PieceWorkSettingDocument } from "src/piece-work/schema/piece-work-setting.schema";
import { PieceWorkDocument } from "src/piece-work/schema/piece-work.schema";
import { MarketingChannelDocument } from "src/marketing-setting/schema/channel.schema.dto";
import { InputDocument } from "src/project/schema/input.schema";
import { OrderDocument } from "src/project/schema/order.schema";
import { ProjectTypeDocument } from "src/project/schema/project-type.schema";
import { ProjectDocument } from "src/project/schema/project.schema";
import { TaskDocument } from "src/project/schema/task.schema";
import {
    activeCrewMember,
    add2Array,
    arraysAreEqual,
    calcCrewLeadBonus,
    dedupeArray,
    dedupePwObjects,
    dynamicSort,
    findCrewLeadId,
    findCurrentWage,
    getWeekStart,
    isUUID,
    profitScoreCalc,
    roundTo1,
    roundTo2,
    roundTo3,
    shortenDate,
    sumArray,
    getActualCostBetweenDates,
    getActualCostByDates,
} from "src/shared/helpers/logics";
import OkResponse from "src/shared/http/response/ok.http";
import { SubcontractorDocument } from "src/subcontractor/schema/subcontractor.schema";
import { SubcontractorService } from "src/subcontractor/subcontractor.service";
import { TimeCard, TimeCardDocument } from "src/time-card/schema/time-card.schema";
import { WorkTaskDocument } from "src/work-task/schema/work-task.schema";
import { WeeklyProjectReportDto } from "./dto/weekly-project-report.dto";
import { TaskTypeEnum } from "src/time-card/enum/task-type.enum";
import { CustomProjectDocument } from "src/custom-project/schema/custom-project.schema";
import { LeadSourceDocument } from "src/marketing-setting/schema/lead-source.schema";
import { CampaignDocument } from "src/marketing-setting/schema/campaign.schema";
import { DailyLogService } from "src/daily-log/daily-log.service";
import { LeadDocument } from "src/lead/schema/lead.schema";
import { PermissionsEnum } from "src/shared/enum/permission.enum";
import { PositionService } from "src/position/position.service";
import { ContactDocument } from "src/contacts/schema/contact.schema";
import { ContactTypeEnum } from "src/contacts/enum/contact.enum";
import { symbol } from "joi";

@Injectable()
export class ReportService {
    constructor(
        private readonly crewService: CrewService,
        private readonly subcontractorService: SubcontractorService,
        private readonly dailyLogService: DailyLogService,
        @InjectModel("Member") private readonly memberModel: Model<MemberDocument>,
        @InjectModel("CrmStage") private readonly crmStageModel: Model<CrmStageDocument>,
        @InjectModel("Opportunity") private readonly opportunityModel: Model<OpportunityDocument>,
        @InjectModel("CrmCheckpoint") private readonly checkpointModel: Model<CrmCheckpointDocument>,
        @InjectModel("Crew") private readonly crewModel: Model<CrewDocument>,
        @InjectModel("TimeCard") private readonly timesheetModel: Model<TimeCardDocument>,
        @InjectModel("Subcontractor") private readonly subcontractorModel: Model<SubcontractorDocument>,
        @InjectModel("DailyLog") private readonly dailyLogModel: Model<DailyLogDocument>,
        @InjectModel("Project") private readonly projectModel: Model<ProjectDocument>,
        @InjectModel("Order") private readonly orderModel: Model<OrderDocument>,
        @InjectModel("CompanySetting") private readonly companySettingModel: Model<CompanySettingDocument>,
        @InjectModel("ProjectType") private readonly projectTypeModel: Model<ProjectTypeDocument>,
        @InjectModel("PieceWork") private readonly pieceWorkModel: Model<PieceWorkDocument>,
        @InjectModel("PieceWorkSetting")
        private readonly pieceWorkSettingModel: Model<PieceWorkSettingDocument>,
        @InjectModel("CompanyPay")
        private readonly payModel: Model<CompanyPayDocument>,
        @InjectModel("PaySchedule")
        private readonly payScheduleModel: Model<PayScheduleDocument>,
        @InjectModel("Input") private readonly inputModel: Model<InputDocument>,
        @InjectModel("Task") private readonly taskModel: Model<TaskDocument>,
        @InjectModel("WorkTask") private readonly workTaskModel: Model<WorkTaskDocument>,
        @InjectModel("Compensation") private readonly compensationModel: Model<CompensationDocument>,
        @InjectModel("CommissionModification")
        private readonly commissionModificationModel: Model<CommissionModificationDocument>,
        @InjectModel("CustomProject")
        private readonly customProjectModel: Model<CustomProjectDocument>,
        @InjectModel("Lead") private readonly leadsModel: Model<LeadDocument>,
        @InjectModel("Contact") private readonly contactModel: Model<ContactDocument>,
        @InjectModel("LeadSource") private readonly leadSourceModel: Model<LeadSourceDocument>,
        @InjectModel("Campaign") private readonly campaignModel: Model<CampaignDocument>,
        @InjectModel("MarketingChannel")
        private readonly channelModel: Model<MarketingChannelDocument>,
        private readonly positionService: PositionService,
    ) {}

    getDataBySalesTeam(
        opps: any,
        prop: string,
        startDate: Date,
        endDate: Date,
        salesTeam: any[],
        companyId: string,
        leadTypes: any,
    ) {
        try {
            const dateStart = new Date(startDate);
            const dateEnd = new Date(endDate);
            // Periods to include in report
            const totalPeriods = 4;
            // Length in days of a period
            const length = 7;
            // Define lead types - TODO: make this a user setting

            // Initiate report
            const report: any = {};
            report.num = [];
            report.selfGen = [];
            report.converted = [];
            // report.opps = [];
            report.actions = [];
            report.types = [];
            report.salesPeople = [];
            const salesPeople = [];
            // Go through each lead type
            for (const type of leadTypes) {
                const typeObj: any = {};
                typeObj.id = type._id;
                typeObj.name = type.name;
                typeObj.typeReplacement = type.typeReplacement;
                typeObj.count = 0; // it should be of first period only
                typeObj.num = [];
                typeObj.selfGen = [];
                typeObj.converted = [];
                typeObj.opps = [];
                typeObj.actions = [];
                typeObj.salesPeople = [];
                // Go through each member of sales team
                for (const member of salesTeam) {
                    const memberObj: any = {};
                    memberObj.id = member._id;
                    memberObj.salesPersonName = member.name;
                    memberObj.stage = prop;
                    memberObj.type = type._id;
                    memberObj.num = [];
                    memberObj.selfGen = [];
                    memberObj.converted = [];
                    memberObj.opps = [];
                    memberObj.actions = [];
                    // Go through each period starting with current
                    for (let i = 0; i < totalPeriods; i++) {
                        const start = new Date(dateStart);
                        const end = new Date(dateEnd);
                        start.setDate(start.getDate() - i * length);
                        end.setDate(end.getDate() - i * length);
                        const pObj: any = {};
                        pObj.num = 0;
                        pObj.selfGen = 0;
                        pObj.converted = 0;
                        pObj.opps = [];
                        pObj.actions = [];
                        // If looking for 'actions' this loop,
                        if (prop === "actions") {
                            for (const opp of opps) {
                                let oppAction = false;
                                opp.actions &&
                                    opp.actions.map((action) => {
                                        if (
                                            new Date(action.completedAt) >= start &&
                                            new Date(action.completedAt) <= end &&
                                            opp.oppType === type._id &&
                                            action.completedBy === member._id
                                        ) {
                                            action.name = opp?.lastName
                                                ? `${opp?.lastName}, ${opp.firstName}`
                                                : opp.firstName;
                                            action.oppId = opp._id;
                                            action.stage = opp.stage;
                                            pObj.num++;
                                            pObj.actions.push(action);
                                            oppAction = true;
                                        }
                                    });
                                if (oppAction) pObj.opps.push(opp);
                            }
                        } else if (prop === "lostDate") {
                            for (const opp of opps) {
                                if (!opp[prop]) continue;
                                const checkDate = opp[prop];
                                if (
                                    new Date(checkDate) >= start &&
                                    new Date(checkDate) <= end &&
                                    opp.oppType === type._id &&
                                    member._id === opp.salesPerson
                                ) {
                                    pObj.num++;
                                    pObj.opps.push(opp);
                                    // if (opp.referredBy === member._id) {
                                    //     opp.selfGen = true;
                                    //     pObj.selfGen++;
                                    // }
                                    if (opp.selfGen) pObj.selfGen++;
                                }
                            }
                        } else if (prop === "newLeadDate") {
                            // this is for leads collection
                            for (const opp of opps) {
                                const checkDate = opp[prop];
                                const csr = opp?.csrId;

                                if (
                                    new Date(checkDate) >= start &&
                                    new Date(checkDate) <= end &&
                                    member._id === csr &&
                                    (opp.workType === type._id || opp.oppType === type._id)
                                ) {
                                    pObj.num++;
                                    pObj.opps.push(opp);
                                    if (opp.converted) pObj.converted++;
                                    if (opp.selfGen) pObj.selfGen++;
                                }
                            }
                        } else {
                            // If looking for a prop different than 'actions' & 'lostDate', this loop
                            for (const opp of opps) {
                                // if (!opp?.checkpointActivity) continue;
                                // const checkDate = opp?.checkpointActivity[prop]?.split("@")[0];

                                // not using checkpoin history for sales person
                                const checkDate = opp[prop];
                                const salesP = opp?.salesPerson;
                                // // sales person who completed this checkpoint
                                // const salesP = opp?.checkpointActivity[prop]?.split("@")[1];

                                if (
                                    new Date(checkDate) >= start &&
                                    new Date(checkDate) <= end &&
                                    opp.oppType === type._id &&
                                    member._id === salesP
                                ) {
                                    pObj.num++;
                                    pObj.opps.push(opp);
                                    // if (opp.referredBy === member._id) {
                                    //     opp.selfGen = true;
                                    //     pObj.selfGen++;
                                    // }
                                    if (opp.selfGen) pObj.selfGen++;
                                }
                            }
                        }
                        // Add results to the member
                        memberObj.num.push(pObj.num);
                        memberObj.selfGen.push(pObj.selfGen);
                        memberObj.converted.push(pObj.converted);
                        memberObj.opps.push(pObj.opps);
                        memberObj.actions.push(pObj.actions);
                    }
                    // Add member to the lead type
                    typeObj.salesPeople.push(memberObj);
                    // Add member to report for totals
                    salesPeople.push(memberObj);
                }
                // Go through the periods and sum each member array for the type
                for (let i = 0; i < totalPeriods; i++) {
                    let num = 0;
                    let selfGen = 0;
                    let converted = 0;
                    const opps = [];
                    const actions = [];
                    typeObj.salesPeople.map((member) => {
                        num += member.num[i];
                        selfGen += member.selfGen[i];
                        converted += member.converted[i];
                        opps.push(...member.opps[i]);
                        actions.push(...member.actions[i]);
                    });
                    typeObj.num.push(num);
                    typeObj.count = typeObj.num[0];
                    typeObj.selfGen.push(selfGen);
                    typeObj.converted.push(converted);
                    typeObj.opps.push(opps);
                    typeObj.actions.push(actions);
                }
                report.types.push(typeObj);
            }
            // Go through the periods and sum each type array for the report
            for (let i = 0; i < totalPeriods; i++) {
                let num = 0;
                let selfGen = 0;
                let converted = 0;
                const opps = [];
                const actions = [];
                // const salesPeople = [];
                report.types.map((type) => {
                    const memObj: any = {};
                    memObj.num = 0;
                    num += type.num[i];
                    selfGen += type.selfGen[i];
                    converted += type.converted[i];
                    opps.push(...type.opps[i]);
                    actions.push(...type.actions[i]);
                });
                report.num.push(num);
                report.selfGen.push(selfGen);
                report.converted.push(converted);
                // report.opps.push(opps);
                report.actions.push(actions);
            }
            for (const member of salesTeam) {
                const memberObj: any = {};
                memberObj.id = member._id;
                memberObj.salesPersonName = member.name;
                memberObj.stage = prop;
                memberObj.num = [];
                memberObj.selfGen = [];
                memberObj.converted = [];
                memberObj.opps = [];
                memberObj.actions = [];
                for (let i = 0; i < totalPeriods; i++) {
                    const personObj: any = {};
                    personObj.num = 0;
                    personObj.selfGen = 0;
                    personObj.converted = 0;
                    personObj.opps = [];
                    personObj.actions = [];
                    for (const person of salesPeople) {
                        if (member._id === person.id) {
                            personObj.num += person.num[i];
                            personObj.selfGen += person.selfGen[i];
                            personObj.converted += person.converted[i];
                            personObj.opps.push(...person.opps[i]);
                            personObj.actions.push(...person.actions[i]);
                        }
                    }
                    memberObj.num.push(personObj.num);
                    memberObj.selfGen.push(personObj.selfGen);
                    memberObj.converted.push(personObj.converted);
                    memberObj.opps.push(personObj.opps);
                    memberObj.actions.push(personObj.actions);
                }
                report.salesPeople.push(memberObj);
            }
            return report;
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // Collect all opportunites where 1 property is before a date and
    // the other is after or doesn't exist yet
    getReportBetween(opps, prop, prop2, dateEnd, leadTypes) {
        try {
            const data = {
                totalNum: 0,
                vol: 0,
                RR: 0,
                types: [],
            };
            for (const type of leadTypes) {
                const report: any = {};
                report.id = type._id;
                report.name = type.name;
                report.typeReplacement = type.typeReplacement;
                report.num = 0;
                report.opps = [];
                report.vol = 0;
                report.RR = 0;

                opps.map((opp) => {
                    const propValue = opp[prop];
                    const prop2Value = opp[prop2];

                    if (
                        propValue &&
                        new Date(propValue) <= dateEnd &&
                        opp?.orderId &&
                        (!prop2Value || new Date(prop2Value) > dateEnd)
                    ) {
                        const projectTypes = opp.projectType;
                        if (projectTypes._id === type._id) {
                            report.num++;
                            report.opps.push(opp);
                            report.vol += opp.soldValue;
                            report.RR += opp.realRevValue;
                        }
                    }
                });
                data.totalNum += report.num;
                data.vol += report.vol;
                data.RR += report.RR;

                data.types.push(report);
            }

            return data;
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // Collect all opportunites where a prop is between a start and end date
    getReportCountByOppType(
        opps,
        prop,
        dateStart: Date,
        dateEnd: Date,
        salesPersonId: string[],
        leadTypes,
        filterBy: "salesPerson" | "csr",
    ) {
        try {
            const data: any = {
                totalNum: 0,
                selfGenCount: 0,
                vol: 0,
                RR: 0,
                types: [],
            };

            if (prop === "saleDate") {
                opps = opps.filter((o) => o.status === OpportunityStatusEnum.Active);
                data["tMat"] = 0;
                data["tLab"] = 0;
                data["tCom"] = 0;
                data["tDis"] = 0;
                data["tFin"] = 0;
            }
            let volWithotRepair = 0;
            for (const type of leadTypes) {
                const report: any = {};
                report.id = type._id;
                report.name = type.name;
                report.typeReplacement = type.typeReplacement;
                report.num = 0;
                report.opps = [];
                report.vol = 0;
                report.RR = 0;
                report.selfGenCount = 0;
                if (prop === "saleDate") {
                    report.tMat = 0;
                    report.tLab = 0;
                    report.tCom = 0;
                    report.tDis = 0;
                    report.disPer = 0;
                    report.tFin = 0;
                }
                report.score = 0;
                for (let i = 0; i < opps.length; i++) {
                    // date on which checkpoint is completed
                    const checkDate = prop !== "lostDate" ? opps[i][prop] : opps[i]?.lostDate;
                    // not using checkpoint history for sales person
                    // const salesP = opps[i]?.salesPerson;
                    const salesP = filterBy === "salesPerson" ? opps[i]?.salesPerson : opps[i]?.csrId;
                    // deleting these fields as not needed
                    delete opps[i].nextAction;
                    delete opps[i].actions;

                    if (
                        new Date(checkDate) >= dateStart &&
                        new Date(checkDate) <= dateEnd &&
                        salesPersonId.includes(salesP)
                    ) {
                        // const projectTypes =
                        //     prop === "saleDate" ? opps[i]?.acceptedType : opps[i]?.oppType?._id;
                        if (opps[i]?.oppType?._id === type._id || opps[i]?.workType === type._id) {
                            report.num++;
                            report.opps.push(opps[i]);

                            if (prop === "saleDate" && opps[i]?.order) {
                                const { chngOrder, chngOrderLabor, chngOrderMat } = opps[
                                    i
                                ]?.changeOrders?.reduce(
                                    (acc, obj) => {
                                        if (obj?.deleted === false && (obj?.signedBySales || obj.total < 0)) {
                                            acc.chngOrder += obj.jobCost || 0; // Adding total without tax
                                            acc.chngOrderLabor += obj.labor || 0; // Adding labor
                                            acc.chngOrderMat += obj.materials || 0; // Adding materials
                                        }
                                        return acc;
                                    },
                                    { chngOrder: 0, chngOrderLabor: 0, chngOrderMat: 0 },
                                ) || { chngOrder: 0, chngOrderLabor: 0, chngOrderMat: 0 };

                                // adding extra work to volume

                                opps[i].soldValue += chngOrder;
                                opps[i]["score"] = 0;
                                opps[i]["disPer"] = 0;
                                const { priceTotals } = opps[i].order;
                                const discount = opps[i]?.discount ?? 0;
                                const soldValue = opps[i]?.soldValue ?? 0;

                                // Calculating discount percentage
                                report.tDis += discount;
                                opps[i]["discount"] = discount;
                                const totalValue = soldValue + discount;
                                opps[i]["disPer"] = totalValue !== 0 ? (discount / totalValue) * 100 : 0; // Avoid division by zero

                                //TODO: hardcoded type name calc score except Roof Repair
                                if (type.name !== "Roof Repair") {
                                    // Using nullish coalescing for totals to ensure values are always available
                                    volWithotRepair += soldValue;
                                    const mTotal = priceTotals?.mTotal + chngOrderMat ?? 0;
                                    const lTotal = priceTotals?.lTotal + chngOrderLabor ?? 0;
                                    const commission = priceTotals?.commission ?? 0;
                                    const financeFee = opps[i]?.financeFee ?? 0;

                                    // Updating report totals
                                    report.tMat += mTotal;
                                    report.tLab += lTotal;
                                    report.tCom += commission;
                                    report.tFin += financeFee;

                                    opps[i]["score"] = opps[i]?.budgetScore || 0;
                                    // profitScoreCalc(
                                    //     soldValue,
                                    //     mTotal,
                                    //     lTotal,
                                    //     commission,
                                    //     financeFee,
                                    // );
                                }
                            }

                            report.vol += opps[i]?.soldValue || 0;
                            report.RR += opps[i]?.realRevValue || 0;

                            if (opps[i]?.selfGen) {
                                data.selfGenCount++;
                                report.selfGenCount++;
                            }
                        }
                    }
                }
                data.totalNum += report.num;
                data.vol += report.vol;
                data.RR += report.RR;
                if (prop === "saleDate") {
                    //NOTE: 10 & 20 (custom values added for calculation)
                    // for score calc we are not including repair volume
                    report.score = profitScoreCalc(
                        volWithotRepair,
                        report.tMat,
                        report.tLab,
                        report.tCom,
                        report.tFin,
                    );

                    report.disPer = (report.tDis / (report.vol + report.tDis)) * 100 || 0;

                    data["tDis"] += report.tDis;
                    data["tMat"] += report.tMat;
                    data["tLab"] += report.tLab;
                    data["tCom"] += report.tCom;
                    data["tFin"] += report.tFin;
                }
                data.types.push(report);
            }

            return data;
        } catch (e) {
            console.log(e);
        }
    }

    async weeklySalesReport(userId: string, companyId: string, reportStart: Date, reportEnd: Date) {
        try {
            const startDate = new Date(reportStart);
            const endDate = new Date(reportEnd);
            const twoMonthPastDate = new Date(reportStart);
            twoMonthPastDate.setDate(twoMonthPastDate.getDate() - 150);
            // const checkpoints = await this.checkpointModel
            //     .find({
            //         companyId,
            //         stageGroup: StageGroupEnum.Sales,
            //         deleted: { $ne: true },
            //     })
            //     .select("_id name symbol stageGroup sequence")
            //     .sort({ sequence: 1 });

            // code for dynamic query
            // const dateFields = checkpoints.map((cp) => cp.symbol);
            // const orQuery: any = dateFields.map((field) => ({
            //     $or: [
            //         {
            //             [field]: {
            //                 $gte: twoMonthPastDate,
            //                 $lte: endDate,
            //             },
            //         },
            //         {
            //             [field]: {
            //                 $exists: false,
            //             },
            //         },
            //     ],
            // }));

            // // Add any additional static fields to the query
            // orQuery.push(
            //     {
            //         $or: [
            //             {
            //                 lostDate: {
            //                     $gte: twoMonthPastDate,
            //                     $lte: endDate,
            //                 },
            //             },
            //             {
            //                 lostDate: {
            //                     $exists: false,
            //                 },
            //             },
            //         ],
            //     },
            //     {
            //         $or: [
            //             {
            //                 newLeadDate: {
            //                     $gte: twoMonthPastDate,
            //                     $lte: endDate,
            //                 },
            //             },
            //             {
            //                 newLeadDate: {
            //                     $exists: false,
            //                 },
            //             },
            //         ],
            //     },
            //     {
            //         $or: [
            //             {
            //                 jobStartedDate: {
            //                     $gte: twoMonthPastDate,
            //                     $lte: endDate,
            //                 },
            //             },
            //             {
            //                 jobStartedDate: {
            //                     $exists: false,
            //                 },
            //             },
            //         ],
            //     },
            //     {
            //         $or: [
            //             {
            //                 actions: {
            //                     $elemMatch: {
            //                         completedAt: {
            //                             $gte: twoMonthPastDate,
            //                             $lte: endDate,
            //                         },
            //                     },
            //                 },
            //             },
            //             {
            //                 actions: {
            //                     $elemMatch: {
            //                         completedAt: {
            //                             $exists: false,
            //                         },
            //                     },
            //                 },
            //             },
            //         ],
            //     },
            // );

            const [salesTeam, allOpps, allProjectTypes, allLeads, csrTeam, checkpoints] = await Promise.all([
                this.memberModel.aggregate([
                    {
                        $match: {
                            company: companyId,
                            hireDate: { $lte: reportEnd },
                            $or: [
                                { deleted: false },
                                {
                                    $and: [
                                        { terminateDate: { $exists: true } },
                                        { terminateDate: { $gte: reportStart } },
                                    ],
                                },
                            ],
                        },
                    },
                    {
                        $lookup: {
                            from: "Compensation",
                            localField: "_id",
                            foreignField: "memberId",
                            as: "result",
                        },
                    },
                    {
                        $lookup: {
                            from: "Position",
                            localField: "result.positionId",
                            foreignField: "_id",
                            as: "position",
                        },
                    },
                    {
                        $match: {
                            "position.symbol": {
                                $in: ["SalesManager", "SalesPerson", "GeneralManager", "RRTech"],
                            },
                        },
                    },
                    {
                        $addFields: {
                            name: {
                                $cond: {
                                    if: {
                                        $and: [
                                            { $ifNull: ["$preferredName", false] },
                                            { $ne: ["$preferredName", ""] },
                                        ],
                                    },
                                    then: {
                                        $concat: [
                                            "$preferredName",
                                            " ",
                                            {
                                                $arrayElemAt: [{ $split: ["$name", " "] }, 1],
                                            },
                                        ],
                                    },
                                    else: "$name",
                                },
                            },
                        },
                    },
                    {
                        $project: {
                            result: 0,
                            position: 0,
                        },
                    },
                ]),
                this.opportunityModel.aggregate([
                    {
                        $match: {
                            deleted: false,
                            companyId,
                            // status: { $ne: "inactive" },
                            warrantyType: { $ne: true },
                            // $or: orQuery,
                            $or: [
                                {
                                    oppDate: {
                                        $gte: twoMonthPastDate,
                                        $lte: endDate,
                                    },
                                },
                                {
                                    actions: {
                                        $elemMatch: {
                                            completedAt: {
                                                $gte: twoMonthPastDate,
                                                $lte: endDate,
                                            },
                                        },
                                    },
                                },
                            ],
                        },
                    },
                    {
                        $set: {
                            oppType: {
                                $cond: {
                                    if: {
                                        $and: [
                                            { $gt: ["$saleDate", null] },
                                            { $gt: ["$acceptedType", null] },
                                        ],
                                    },
                                    then: "$acceptedType",
                                    else: "$oppType",
                                },
                            },
                        },
                    },
                    {
                        $lookup: {
                            from: "ProjectType",
                            foreignField: "_id",
                            localField: "acceptedType",
                            as: "projectType",
                            pipeline: [
                                {
                                    $project: {
                                        _id: 1,
                                        name: 1,
                                        typeReplacement: 1,
                                    },
                                },
                            ],
                        },
                    },
                    {
                        $unwind: {
                            path: "$projectType",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Contact", // Changed from Contact
                            pipeline: [
                                {
                                    $project: {
                                        _id: 1,
                                        fullName: 1,
                                        businessName: 1,
                                        isBusiness: 1,
                                    },
                                },
                            ],
                            foreignField: "_id",
                            localField: "contactId",
                            as: "contactId",
                        },
                    },
                    {
                        $unwind: {
                            path: "$contactId",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "LeadSource",
                            localField: "leadSourceId",
                            foreignField: "_id",
                            pipeline: [{ $project: { name: 1 } }],
                            as: "leadSourceObj",
                        },
                    },
                    {
                        $addFields: {
                            fullName: {
                                $cond: [
                                    { $eq: ["$contactId.isBusiness", true] },
                                    "$contactId.businessName",
                                    "$contactId.fullName",
                                ],
                            },
                        },
                    },
                    {
                        $project: {
                            order: 0,
                            comments: 0,
                            activities: 0,
                            stepsChecklist: 0,
                            leadSourceObj: 0,
                            taxJurisdiction: 0,
                            assignedTo: 0,
                            oppNotes: 0,
                            // status: 0,
                            type: 0,
                            workingCrew: 0,
                            // acceptedType: 0,
                            acceptedProjectId: 0,
                            discount: 0,
                            editedBy: 0,
                            duration: 0,
                            companyAddress: 0,
                            companyLat: 0,
                            companyLang: 0,
                            street: 0,
                            // city: 0,
                            zip: 0,
                            __v: 0,
                            deleted: 0,
                            createdBy: 0,
                        },
                    },
                    // { $sort: { createdAt: -1 } },
                ]),
                this.projectTypeModel
                    .find({
                        companyId,
                        deleted: false,
                    })
                    .select("_id name typeReplacement"),
                this.leadsModel.aggregate([
                    {
                        $match: {
                            companyId,
                            $or: [{ deleted: false }, { deleted: true, oppId: { $exists: true } }],
                            newLeadDate: {
                                $gte: twoMonthPastDate,
                                $lte: endDate,
                            },
                        },
                    },
                    {
                        $lookup: {
                            from: "ProjectType",
                            foreignField: "_id",
                            localField: "workType",
                            as: "projectType",
                            pipeline: [
                                {
                                    $project: {
                                        _id: 1,
                                        name: 1,
                                        typeReplacement: 1,
                                    },
                                },
                            ],
                        },
                    },
                    {
                        $lookup: {
                            from: "Contact",
                            pipeline: [
                                {
                                    $project: {
                                        _id: 1,
                                        fullName: 1,
                                        businessName: 1,
                                        isBusiness: 1,
                                    },
                                },
                            ],
                            foreignField: "_id",
                            localField: "contactId",
                            as: "contactId",
                        },
                    },
                    {
                        $unwind: {
                            path: "$contactId",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $unwind: {
                            path: "$projectType",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "LeadSource",
                            localField: "leadSourceId",
                            foreignField: "_id",
                            pipeline: [{ $project: { name: 1 } }],
                            as: "leadSourceObj",
                        },
                    },
                    {
                        $addFields: {
                            fullName: {
                                $cond: [
                                    { $eq: ["$contactId.isBusiness", true] },
                                    "$contactId.businessName",
                                    "$contactId.fullName",
                                ],
                            },
                            leadSource: { $arrayElemAt: ["$leadSourceObj.name", 0] },
                            converted: {
                                $cond: { if: { $ifNull: ["$oppId", false] }, then: true, else: false },
                            },
                        },
                    },
                    {
                        $project: {
                            contactId: 1,
                            leadSource: 1,
                            converted: 1,
                            newLeadDate: 1,
                            projectType: 1,
                            csrId: 1,
                            createdAt: 1,
                            status: 1,
                            oppId: 1,
                            oppDate: 1,
                            referredBy: 1,
                            lostDate: 1,
                            nextAction: 1,
                            actions: 1,
                            selfGen: 1,
                            workType: 1,
                            fullName: 1,
                        },
                    },
                    // { $sort: { createdAt: -1 } },
                ]),
                this.memberModel.aggregate([
                    {
                        $lookup: {
                            from: "Department",
                            localField: "departmentId",
                            foreignField: "_id",
                            as: "result",
                        },
                    },
                    {
                        $unwind: { path: "$result", preserveNullAndEmptyArrays: false },
                    },
                    {
                        $match: {
                            company: companyId,
                            hireDate: { $lte: reportEnd },
                            // departmentId: "57c3fa9e-6f52-4308-afb2-e481e10c8d70",
                            $or: [
                                { deleted: false },
                                {
                                    $and: [
                                        { terminateDate: { $exists: true } },
                                        { terminateDate: { $gte: reportStart } },
                                    ],
                                },
                            ],
                            "result.name": "Office",
                        },
                    },
                    {
                        $addFields: {
                            name: {
                                $cond: {
                                    if: {
                                        $and: [
                                            { $ifNull: ["$preferredName", false] },
                                            { $ne: ["$preferredName", ""] },
                                        ],
                                    },
                                    then: {
                                        $concat: [
                                            "$preferredName",
                                            " ",
                                            {
                                                $arrayElemAt: [{ $split: ["$name", " "] }, 1],
                                            },
                                        ],
                                    },
                                    else: "$name",
                                },
                            },
                        },
                    },
                    {
                        $project: {
                            result: 0,
                        },
                    },
                ]),
                this.checkpointModel
                    .find({
                        companyId,
                        stageGroup: StageGroupEnum.Sales,
                        deleted: { $ne: true },
                    })
                    .select("_id name symbol stageGroup sequence")
                    .sort({ sequence: 1 }),
            ]);

            // Convert each document to a plain JavaScript object
            const opps = allOpps.map((opp) => JSON.parse(JSON.stringify(opp)));
            const actions = this.getDataBySalesTeam(
                opps,
                "actions",
                startDate,
                endDate,
                salesTeam,
                companyId,
                allProjectTypes,
            );
            actions.sequence = 1;

            const report: any = {};
            const dynamicVars = {};
            // let leads;
            let newOpps;
            let sales;

            // for leads calculation
            const leads = this.getDataBySalesTeam(
                [...allLeads, ...opps],
                "newLeadDate",
                startDate,
                endDate,
                csrTeam,
                companyId,
                [...allProjectTypes, { _id: "unknown", name: "Unknown", typeReplacement: false }],
            );

            leads.types.map((type) => {
                const sourceArray = type.opps[0].map((opp) => opp.leadSource);
                const sources = dedupeArray(sourceArray);
                type.leadSources = [];
                sources.map((source) => {
                    const obj: any = {};
                    obj.id = source;
                    obj.num = 0;
                    obj.cost = 0;
                    type.opps[0].map((opp) => {
                        if (opp.leadSource === source) {
                            obj.num++;
                            obj.cost += opp.leadCost || 0;
                        }
                    });
                    type.leadSources.push(obj);
                });
            });

            leads.sequence = 0;
            report["New Leads"] = leads;

            // code for dynamic checkpont report
            checkpoints.map((data) => {
                //these are always defalut
                if (data.symbol === "oppDate") {
                    newOpps = this.getDataBySalesTeam(
                        opps,
                        data.symbol,
                        startDate,
                        endDate,
                        salesTeam,
                        companyId,
                        allProjectTypes,
                    );
                    newOpps.sequence = data.sequence + 1;
                    report[data.name] = newOpps;
                    // leadsName = data.name.replace(/\s+/g, "");
                } else if (data.symbol === "saleDate") {
                    sales = this.getDataBySalesTeam(
                        opps.filter((o) => o.status === OpportunityStatusEnum.Active),
                        data.symbol,
                        startDate,
                        endDate,
                        salesTeam,
                        companyId,
                        allProjectTypes,
                    );

                    // Add sold values to sales
                    sales.sold = [];
                    // sales.opps.map((pOpps) => {
                    //     const soldValue = sumArray(pOpps, "soldValue");
                    //     sales.sold.push(soldValue);
                    // });
                    sales.types.map((type) => {
                        type.sold = [];
                        type.opps.map((pOpps) => {
                            const soldValue = sumArray(pOpps, "soldValue");
                            type.sold.push(soldValue);
                        });
                        const totalSold = add2Array(sales.sold, type.sold);

                        // updating total sold array
                        sales.sold = totalSold;

                        type.salesPeople.map((person) => {
                            person.sold = [];
                            person.opps.map((pOpps) => {
                                const soldValue = sumArray(pOpps, "soldValue");
                                person.sold.push(soldValue);
                            });
                        });
                    });
                    sales.salesPeople.map((person) => {
                        person.sold = [];
                        person.opps.map((pOpps) => {
                            const soldValue = sumArray(pOpps, "soldValue");
                            person.sold.push(soldValue);
                        });
                    });

                    sales.sequence = data.sequence + 1;
                    report[data.name] = sales;
                    // salesName = data.name.replace(/\s+/g, "");
                } //for rest of checkpoints
                else {
                    dynamicVars[data.name] = this.getDataBySalesTeam(
                        opps,
                        data.symbol,
                        startDate,
                        endDate,
                        salesTeam,
                        companyId,
                        allProjectTypes,
                    );
                    dynamicVars[data.name].sequence = data.sequence + 1;
                }
            });

            const lost = this.getDataBySalesTeam(
                opps,
                "lostDate",
                startDate,
                endDate,
                salesTeam,
                companyId,
                allProjectTypes,
            );
            lost.sequence = checkpoints.length + 2;

            report.actions = actions;
            report["Lead Lost"] = lost;

            newOpps.types.map((type) => {
                const sourceArray = type.opps[0].map((opp) => opp.leadSource);
                const sources = dedupeArray(sourceArray);
                type.leadSources = [];
                sources.map((source) => {
                    const obj: any = {};
                    obj.id = source;
                    obj.num = 0;
                    obj.cost = 0;
                    type.opps[0].map((opp) => {
                        if (opp.leadSource === source) {
                            obj.num++;
                            obj.cost += opp.leadCost || 0;
                        }
                    });
                    type.leadSources.push(obj);
                });
            });

            if (Object.keys(dynamicVars).length > 0) {
                Object.assign(report, dynamicVars);
            }

            const upcoming: any = this.getReportBetween(
                opps,
                "saleDate",
                "jobStartedDate",
                endDate,
                allProjectTypes,
            );

            // Upcoming Jobs report
            report.upcoming = {};
            report.upcoming.sequence = checkpoints.length + 3;
            report.upcoming.num = upcoming.totalNum;
            report.upcoming.types = upcoming.types;
            report.upcomingVol = roundTo2(upcoming.vol);
            report.upcomingRR = roundTo2(upcoming.RR);

            // convert opp code
            const conversion: any = {};
            const checkpointsArr = [
                { name: "New Leads", symbol: "newLeadDate", sequence: 0, _id: 1 },
                ...checkpoints,
            ];
            for (const check of checkpointsArr) {
                conversion[check.name] = {};
                const checkSymbolCount = report[check.name]?.num?.reduce((acc, num) => acc + num, 0);

                for (const ck of checkpointsArr) {
                    if (ck._id !== check._id && ck.sequence > check.sequence) {
                        const ckSymbolCount = report[ck.name]?.num?.reduce((acc, num) => acc + num, 0);
                        conversion[check.name][`${check.name} > ${ck.name}`] = Math.round(
                            (100 * ckSymbolCount) / checkSymbolCount,
                        );
                    }
                }
                if (Object.keys(conversion[check.name]).length === 0) delete conversion[check.name];
            }

            return new OkResponse({ report, conversion });
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getSalesPersonOrCustomReport(
        userId: string,
        companyId: string,
        start: Date,
        end: Date,
        isSalesReport: boolean,
        salesPersonId: string,
        isCSRReport?: boolean,
    ) {
        try {
            const dateStart = new Date(start);
            const dateEnd = new Date(end);
            // const ONE_WEEK = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
            // const isSalesReport = dateEnd.getTime() - dateStart.getTime() <= ONE_WEEK;
            const currentDate = new Date();

            let csrArr = [],
                salesPersonArr = [];

            if (isCSRReport) {
                csrArr = salesPersonId.split(",");
            } else {
                salesPersonArr = salesPersonId.split(",");
            }
            const userFilterArr = salesPersonArr.length > 0 ? salesPersonArr : csrArr;

            let lastStart: Date | null = null;
            let lastEnd: Date | null = null;
            let prevStart: Date | null = null;
            let prevEnd: Date | null = null;
            if (isSalesReport) {
                lastStart = new Date(dateStart);
                lastStart.setDate(lastStart.getDate() - 7);
                lastEnd = new Date(dateEnd);
                lastEnd.setDate(lastEnd.getDate() - 7);
                prevStart = new Date(dateStart);
                prevStart.setDate(prevStart.getDate() - 14);
                prevEnd = new Date(dateEnd);
                prevEnd.setDate(prevEnd.getDate() - 14);
            }

            const checkpointMatchQuery: any = {
                companyId,
                deleted: { $ne: true },
            };
            csrArr.length > 0
                ? (checkpointMatchQuery.stageGroup = { $in: [StageGroupEnum.Sales, StageGroupEnum.Leads] })
                : (checkpointMatchQuery.stageGroup = { $in: [StageGroupEnum.Sales] });

            // db.opportunities.createIndex({ companyId: 1, salesPersonHistory: 1, deleted: 1 });
            const checkpoints = await this.checkpointModel
                .find(checkpointMatchQuery)
                .select("_id name symbol stageGroup sequence")
                .sort({ sequence: 1, name: 1 });

            // code for dynamic query
            const dateFields = checkpoints.map((cp) => cp.symbol);
            const orQuery: any = dateFields.map((field) => ({
                $or: [
                    {
                        [field]: {
                            $gte: prevStart,
                            $lte: dateEnd,
                        },
                    },
                    {
                        [field]: {
                            $exists: false,
                        },
                    },
                ],
            }));

            // Add any additional static fields to the query
            orQuery.push(
                {
                    $or: [
                        {
                            lostDate: {
                                $gte: prevStart,
                                $lte: dateEnd,
                            },
                        },
                        {
                            lostDate: {
                                $exists: false,
                            },
                        },
                    ],
                },
                {
                    $or: [
                        {
                            "nextAction.due": {
                                // $gte: prevStart,
                                $lte: dateEnd,
                            },
                        },
                        {
                            nextAction: {
                                $exists: false,
                            },
                        },
                    ],
                },

                {
                    $or: [
                        {
                            actions: {
                                $elemMatch: {
                                    completedAt: {
                                        $gte: prevStart,
                                        $lte: dateEnd,
                                    },
                                },
                            },
                        },
                        {
                            actions: {
                                $elemMatch: {
                                    completedAt: {
                                        $exists: false,
                                    },
                                },
                            },
                        },
                    ],
                },
            );

            const matchOppQuery: any = {
                companyId,
                deleted: { $ne: true },
                warrantyType: { $ne: true },
                $or: orQuery,
            };

            if (salesPersonArr.length > 0) {
                matchOppQuery.salesPerson = { $in: salesPersonArr };
            } else {
                matchOppQuery.csrId = { $in: csrArr };
            }

            const matchTimecardQuery: any = {
                deleted: { $ne: true },
                timeIn: { $gte: start, $lte: end },
            };

            if (salesPersonArr.length > 0) {
                matchTimecardQuery.memberId = { $in: salesPersonArr };
            } else {
                matchTimecardQuery.memberId = { $in: csrArr };
            }

            const [leadTypes, allStage, opps, timeCards] = await Promise.all([
                this.projectTypeModel.find({ companyId, deleted: false }).select("_id name typeReplacement"),
                this.crmStageModel.find({ companyId, deleted: false }).select("_id name sequence stageGroup"),
                this.opportunityModel.aggregate([
                    {
                        $match: matchOppQuery,
                    },
                    {
                        $set: {
                            oppType: {
                                $cond: {
                                    if: {
                                        $and: [
                                            { $gt: ["$saleDate", null] },
                                            { $gt: ["$acceptedType", null] },
                                        ],
                                    },
                                    then: "$acceptedType",
                                    else: "$oppType",
                                },
                            },
                        },
                    },
                    {
                        $lookup: {
                            from: "ProjectType",
                            foreignField: "_id",
                            localField: "oppType",
                            as: "oppType",
                            pipeline: [
                                {
                                    $project: {
                                        _id: 1,
                                        name: 1,
                                        typeReplacement: 1,
                                    },
                                },
                            ],
                        },
                    },
                    {
                        $unwind: {
                            path: "$oppType",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Contact",
                            pipeline: [
                                {
                                    $project: {
                                        _id: 1,
                                        fullName: 1,
                                        businessName: 1,
                                        isBusiness: 1,
                                    },
                                },
                            ],
                            foreignField: "_id",
                            localField: "contactId",
                            as: "contactId",
                        },
                    },
                    {
                        $unwind: {
                            path: "$contactId",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "LeadSource",
                            localField: "leadSourceId",
                            foreignField: "_id",
                            pipeline: [{ $project: { name: 1 } }],
                            as: "leadSourceObj",
                        },
                    },
                    {
                        $addFields: {
                            fullName: {
                                $cond: [
                                    { $eq: ["$contactId.isBusiness", true] },
                                    "$contactId.businessName",
                                    "$contactId.fullName",
                                ],
                            },
                        },
                    },
                    {
                        $lookup: {
                            from: "Order",
                            localField: "orderId",
                            foreignField: "_id",
                            pipeline: [
                                {
                                    $project: {
                                        priceTotals: 1,
                                        oppId: 1,
                                        actualTotals: 1,
                                        projectPriceId: 1,
                                    },
                                },
                            ],
                            as: "order",
                        },
                    },
                    {
                        $unwind: {
                            path: "$order",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Member",
                            foreignField: "_id",
                            localField: "csrId",
                            as: "csrAssigned",
                            pipeline: [
                                {
                                    $project: {
                                        _id: 1,
                                        name: 1,
                                    },
                                },
                            ],
                        },
                    },
                    {
                        $unwind: {
                            path: "$csrAssigned",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $project: {
                            comments: 0,
                            activities: 0,
                            stepsChecklist: 0,
                            leadSourceObj: 0,
                            taxJurisdiction: 0,
                            assignedTo: 0,
                            oppNotes: 0,
                            type: 0,
                            workingCrew: 0,
                            // acceptedType: 0,
                            acceptedProjectId: 0,
                            editedBy: 0,
                            duration: 0,
                            companyAddress: 0,
                            companyLat: 0,
                            companyLang: 0,
                            street: 0,
                            // city: 0,
                            zip: 0,
                            __v: 0,
                            deleted: 0,
                            createdBy: 0,
                        },
                    },
                    // { $sort: { createdAt: -1 } },
                ]),
                this.timesheetModel.aggregate([
                    {
                        $match: matchTimecardQuery,
                    },
                    {
                        $group: {
                            _id: null,
                            totalHrs: { $sum: "$hrs" }, // Sum up the `hrs` field
                        },
                    },
                    {
                        $project: {
                            _id: 0,
                            totalHrs: 1,
                        },
                    },
                ]),
            ]);

            let leads = [];

            if (isCSRReport) {
                leads = await this.leadsModel.aggregate([
                    {
                        $match: {
                            companyId,
                            csrId: { $in: csrArr },
                            deleted: { $ne: true },
                        },
                    },
                    {
                        $lookup: {
                            from: "ProjectType",
                            foreignField: "_id",
                            localField: "workType",
                            as: "projectType",
                            pipeline: [
                                {
                                    $project: {
                                        _id: 1,
                                        name: 1,
                                        typeReplacement: 1,
                                    },
                                },
                            ],
                        },
                    },
                    {
                        $unwind: {
                            path: "$projectType",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "LeadSource",
                            localField: "leadSourceId",
                            foreignField: "_id",
                            pipeline: [
                                {
                                    $project: {
                                        name: 1,
                                    },
                                },
                            ],
                            as: "leadSourceObj",
                        },
                    },
                    {
                        $lookup: {
                            from: "Contact",
                            pipeline: [
                                {
                                    $project: {
                                        _id: 1,
                                        fullName: 1,
                                        businessName: 1,
                                        isBusiness: 1,
                                    },
                                },
                            ],
                            foreignField: "_id",
                            localField: "contactId",
                            as: "contactId",
                        },
                    },
                    {
                        $unwind: {
                            path: "$contactId",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $addFields: {
                            leadSource: { $arrayElemAt: ["$leadSourceObj.name", 0] },
                            converted: {
                                $cond: {
                                    if: { $ifNull: ["$oppId", false] },
                                    then: true,
                                    else: false,
                                },
                            },
                            // contactId: {
                            //     firstName: "$firstName",
                            //     lastName: "$lastName",
                            // },
                            fullName: {
                                $cond: [
                                    { $eq: ["$contactId.isBusiness", true] },
                                    "$contactId.businessName",
                                    "$contactId.fullName",
                                ],
                            },
                        },
                    },
                    {
                        $project: {
                            contactId: 1,
                            leadSource: 1,
                            converted: 1,
                            newLeadDate: 1,
                            projectType: 1,
                            csrId: 1,
                            createdAt: 1,
                            status: 1,
                            oppId: 1,
                            oppDate: 1,
                            referredBy: 1,
                            lostDate: 1,
                            nextAction: 1,
                            actions: 1,
                            selfGen: 1,
                            workType: 1,
                            fullName: 1,
                        },
                    },
                    // { $sort: { createdAt: -1 } },
                ]);
            }
            const saleStage = allStage.filter((s) => s.stageGroup === StageGroupEnum.Sales).map((s) => s._id);
            const report: any = {};

            report.totalHours = timeCards[0]?.totalHrs || 0;
            let numOverdue = 0;
            let numNoAction = 0;
            opps.forEach((opp: any) => {
                if (
                    opp?.status === OpportunityStatusEnum.Active &&
                    !opp.nextAction &&
                    saleStage.includes(opp.stage)
                )
                    numNoAction++;
                if (
                    opp?.status === OpportunityStatusEnum.Active &&
                    opp.nextAction &&
                    new Date(opp.nextAction.due) < currentDate && // getting action only based on current date
                    saleStage.includes(opp.stage)
                )
                    numOverdue++;
            });
            report.numOverdue = numOverdue;
            report.numNoAction = numNoAction;

            // Actions
            const actions: any = {};
            actions.thisWeek = [];
            actions.thisWeekNum = 0;
            actions.lastWeek = [];
            actions.lastWeekNum = 0;
            actions.prevWeek = [];
            actions.prevWeekNum = 0;
            // Opps w/ actions completed
            const thisWeekOA = [];
            const lastWeekOA = [];
            const prevWeekOA = [];
            // const thisWeekONA = [];

            [...opps, ...leads].map((opp) => {
                // let thisWeekAction = false;
                opp.actions &&
                    opp.actions.map((action) => {
                        if (
                            new Date(action.completedAt) >= dateStart &&
                            new Date(action.completedAt) <= dateEnd &&
                            userFilterArr.includes(action.completedBy)
                        ) {
                            action.name = opp?.contactId?.businessName
                                ? opp?.contactId?.businessName
                                : opp?.contactId?.fullName;
                            actions.thisWeek.push(action);
                            actions.thisWeekNum++;
                            thisWeekOA.push(opp);
                            // thisWeekAction = true;
                        }
                        if (
                            new Date(action.completedAt) >= lastStart &&
                            new Date(action.completedAt) <= lastEnd &&
                            userFilterArr.includes(action.completedBy) &&
                            isSalesReport
                        ) {
                            actions.lastWeek.push(action);
                            actions.lastWeekNum++;
                            lastWeekOA.push(opp);
                            // lastWeekAction = true;
                        }
                        if (
                            new Date(action.completedAt) >= prevStart &&
                            new Date(action.completedAt) <= prevEnd &&
                            userFilterArr.includes(action.completedBy) &&
                            isSalesReport
                        ) {
                            actions.prevWeek.push(action);
                            actions.prevWeekNum++;
                            prevWeekOA.push(opp);
                            // prevWeekAction = true;
                        }
                    });
                // if (!thisWeekAction) thisWeekONA.push(opp);
            });
            actions.thisWeekOA = dedupeArray(thisWeekOA);
            actions.thisWeekOANum = actions.thisWeekOA.length;
            actions.lastWeekOA = dedupeArray(lastWeekOA);
            actions.lastWeekOANum = actions.lastWeekOA.length;
            actions.prevWeekOA = dedupeArray(prevWeekOA);
            actions.prevWeekOANum = actions.prevWeekOA.length;
            //not in use
            // actions.thisWeekONA = dedupeArray(thisWeekONA);
            // actions.thisWeekONANum = actions.thisWeekONA.length;
            report.actions = actions;

            report.noOrderOpp = [];

            const dynamicVars = {};
            report.checkpoints = {};
            let saleNum = 0;
            let oppNum = 0;

            await Promise.all(
                checkpoints.map(async (data) => {
                    const name = data.name;
                    const { symbol } = data;

                    // Get counts for the current period (always required)
                    const currentPeriodPromise = this.getReportCountByOppType(
                        isCSRReport ? [...leads, ...opps] : opps,
                        symbol,
                        dateStart,
                        dateEnd,
                        userFilterArr,
                        symbol === "newLeadDate"
                            ? [...leadTypes, { _id: "unknown", name: "Unknown", typeReplacement: false }]
                            : leadTypes,
                        salesPersonArr.length > 0 ? "salesPerson" : "csr",
                    );

                    let prevWeekPeriodPromise, lastWeekPeriodPromise;

                    if (isSalesReport) {
                        prevWeekPeriodPromise = this.getReportCountByOppType(
                            opps,
                            symbol,
                            prevStart,
                            prevEnd,
                            userFilterArr,
                            leadTypes,
                            salesPersonArr.length > 0 ? "salesPerson" : "csr",
                        );

                        lastWeekPeriodPromise = this.getReportCountByOppType(
                            opps,
                            symbol,
                            lastStart,
                            lastEnd,
                            userFilterArr,
                            leadTypes,
                            salesPersonArr.length > 0 ? "salesPerson" : "csr",
                        );
                    }

                    const [currentPeriod, prevWeekPeriod, lastWeekPeriod] = await Promise.all([
                        currentPeriodPromise,
                        isSalesReport
                            ? prevWeekPeriodPromise
                            : Promise.resolve({ totalNum: 0, selfGenCount: 0 }),
                        isSalesReport
                            ? lastWeekPeriodPromise
                            : Promise.resolve({ totalNum: 0, selfGenCount: 0 }),
                    ]);

                    // Initialize base checkpoint data
                    report.checkpoints[name] = {
                        symbol,
                        sequence: data.sequence,
                        num: currentPeriod.totalNum,
                        types: currentPeriod.types,
                        selfGenCount: currentPeriod.selfGenCount,
                        prevWeek: prevWeekPeriod.totalNum,
                        prevWeekSelfGenCount: prevWeekPeriod.selfGenCount,
                        lastWeek: lastWeekPeriod.totalNum,
                        lasWeekSelfGenCount: lastWeekPeriod.selfGenCount,
                    };

                    // Handle Opportunity checkpoint specific logic
                    if (symbol === "oppDate") {
                        oppNum = currentPeriod.totalNum;
                        report.checkpoints[name].leadSources = {};

                        currentPeriod.types.forEach((leadTyp) => {
                            // Create a frequency map for lead sources
                            const leadSourceMap = leadTyp.opps.reduce((acc, opp) => {
                                if (!acc[opp.leadSource]) {
                                    acc[opp.leadSource] = {
                                        id: opp.leadSource,
                                        num: 0,
                                        cost: 0,
                                        selfGenCount: 0,
                                    };
                                }

                                acc[opp.leadSource].num++;
                                if (opp?.selfGen) acc[opp.leadSource].selfGenCount++;
                                acc[opp.leadSource].cost += Number(opp.leadCost) || 0;

                                return acc;
                            }, {});

                            report.checkpoints[name].leadSources[leadTyp.name] = Object.values(
                                leadSourceMap,
                            ).sort((a: any, b: any) => b.num - a.num);
                        });
                    }

                    // for sale checkpoint only
                    if (symbol === "saleDate") {
                        saleNum = currentPeriod.totalNum;
                        const repairVol =
                            currentPeriod.types?.find((t) => t.name === "Roof Repair")?.vol || 0;

                        // Add sale-specific metrics
                        Object.assign(report.checkpoints[name], {
                            [`${name}Vol`]: currentPeriod.vol,
                            [`${name}RR`]: currentPeriod.RR,
                            [`${name}tMat`]: currentPeriod.tMat,
                            [`${name}tLab`]: currentPeriod.tLab,
                            [`${name}tCom`]: currentPeriod.tCom,
                            [`${name}tDis`]: currentPeriod.tDis,
                            [`${name}tFin`]: currentPeriod.tFin,
                            [`${name}Score`]: profitScoreCalc(
                                currentPeriod.vol - repairVol,
                                currentPeriod.tMat,
                                currentPeriod.tLab,
                                currentPeriod.tCom,
                                currentPeriod.tFin,
                            ),
                            [`${name}DiscountPercent`]: roundTo2(
                                (currentPeriod.tDis / (currentPeriod.vol + currentPeriod.tDis)) * 100,
                            ),
                        });

                        // Check for missing orders
                        currentPeriod.types.forEach((type) => {
                            type.opps.forEach((opp) => {
                                if (!opp?.order) {
                                    report.noOrderOpp.push(`${opp?.PO}-${opp?.num} is missing an order`);
                                }
                            });
                        });
                    }
                }),
            );

            // conversionRate form opp to sale (sale/opp)

            if (isCSRReport) {
                const leadNum = report.checkpoints["New Lead"]?.num;
                const needAssesment = report.checkpoints["Needs Assessment"]?.num;
                report.conversion = {
                    "Lead to Appt: ": leadNum ? roundTo2((needAssesment / leadNum) * 100) : 0,
                    "Appt to Sale: ": oppNum ? roundTo2((saleNum / needAssesment) * 100) : 0,
                    "Lead to sale: ": leadNum ? roundTo2((saleNum / leadNum) * 100) : 0,
                };
            } else {
                report.conversion = {
                    "New Opportunity > Sales": oppNum ? roundTo2((saleNum / oppNum) * 100) : 0,
                };
                for (let i = 0; i < checkpoints.length; i++) {
                    const check1 = checkpoints[i]?.name;
                    const check2 = checkpoints[i + 1]?.name;
                    if (check2)
                        report.conversion[`${check1} > ${check2}`] = roundTo2(
                            (100 * report.checkpoints[check2]?.num) / report.checkpoints[check1]?.num,
                        );
                }
            }

            // Lost Leads Report
            const lostPromise = this.getReportCountByOppType(
                [...leads, ...opps],
                "lostDate",
                dateStart,
                dateEnd,
                userFilterArr,
                [...leadTypes, { _id: "unknown", name: "Unknown", typeReplacement: false }],
                salesPersonArr.length > 0 ? "salesPerson" : "csr",
            );

            let lostPrevPromise, lostLastPromise;

            if (isSalesReport) {
                lostPrevPromise = this.getReportCountByOppType(
                    opps,
                    "lostDate",
                    prevStart,
                    prevEnd,
                    userFilterArr,
                    leadTypes,
                    salesPersonArr.length > 0 ? "salesPerson" : "csr",
                );

                lostLastPromise = this.getReportCountByOppType(
                    opps,
                    "lostDate",
                    lastStart,
                    lastEnd,
                    userFilterArr,
                    leadTypes,
                    salesPersonArr.length > 0 ? "salesPerson" : "csr",
                );
            }

            // Wait for promises to resolve
            const [lost, lostPrev, lostLast] = await Promise.all([
                lostPromise,
                isSalesReport ? lostPrevPromise : Promise.resolve({ totalNum: 0, selfGenCount: 0 }),
                isSalesReport ? lostLastPromise : Promise.resolve({ totalNum: 0, selfGenCount: 0 }),
            ]);

            report.checkpoints.Lost = {};

            report.checkpoints.Lost.symbol = "lostDate";
            report.checkpoints.Lost.sequence = checkpoints.length + 1;
            report.checkpoints.Lost.prevWeek = lostPrev.totalNum;
            report.checkpoints.Lost.lastWeek = lostLast.totalNum;
            report.checkpoints.Lost.num = lost.totalNum;
            report.checkpoints.Lost.types = lost.types;
            report.checkpoints.Lost.selfGenCount = lost.selfGenCount;
            report.checkpoints.Lost.lasWeekSelfGenCount = lostLast.selfGenCount;
            report.checkpoints.Lost.prevWeekSelfGenCount = lostPrev.selfGenCount;

            // lost opp stage data
            report.checkpoints.Lost.stages =
                this.oppByStageCount(opps, allStage, userFilterArr, dateStart, dateEnd).sort(
                    dynamicSort("sequence"),
                ) || [];

            // Return report
            return report;
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async marketingReportCopy(companyId, start, end, user) {
        try {
            const dateStart = new Date(start);
            const dateEnd = new Date(end);
            // Fetch all data in parallel with optimized queries similar to clientValueReport
            const [allLeadSource, allCampaigns, allChannels, opps, coVar, checkpoints, allLeads] =
                await Promise.all([
                    // Get all lead sources
                    this.leadSourceModel.find({
                        companyId,
                    }),
                    // Get all campaigns
                    this.campaignModel.find({
                        companyId,
                        deleted: { $ne: true },
                    }),
                    // Get all channels
                    this.channelModel.find({
                        companyId,
                        deleted: { $ne: true },
                    }),
                    // Get opportunities with all necessary lookups in a single query
                    this.opportunityModel.aggregate([
                        {
                            $match: {
                                companyId,
                                deleted: false,
                                warrantyType: false,
                                $and: [
                                    {
                                        $or: [
                                            { newLeadDate: { $gte: dateStart, $lte: dateEnd } },
                                            { oppDate: { $gte: dateStart, $lte: dateEnd } },
                                            { needsAssessmentDate: { $gte: dateStart, $lte: dateEnd } },
                                            { presentationDate: { $gte: dateStart, $lte: dateEnd } },
                                            { saleDate: { $gte: dateStart, $lte: dateEnd } },
                                        ],
                                    },
                                ],
                            },
                        },
                        {
                            $set: {
                                oppType: {
                                    $cond: {
                                        if: {
                                            $and: [
                                                { $gt: ["$saleDate", null] },
                                                { $gt: ["$acceptedType", null] },
                                            ],
                                        },
                                        then: "$acceptedType",
                                        else: "$oppType",
                                    },
                                },
                            },
                        },
                        {
                            $lookup: {
                                from: "CrmStage",
                                foreignField: "_id",
                                localField: "stage",
                                as: "stageData",
                                pipeline: [{ $project: { _id: 1, stageGroup: 1 } }],
                            },
                        },
                        {
                            $unwind: {
                                path: "$stageData",
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                        {
                            $lookup: {
                                from: "Order",
                                foreignField: "_id",
                                localField: "orderId",
                                as: "order",
                            },
                        },

                        {
                            $unwind: {
                                path: "$order",
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                        {
                            $lookup: {
                                from: "Price",
                                foreignField: "_id",
                                localField: "order.projectPriceId",
                                as: "projectPrice",
                            },
                        },
                        {
                            $unwind: {
                                path: "$projectPrice",
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                        {
                            $lookup: {
                                from: "LeadSource",
                                foreignField: "_id",
                                localField: "leadSourceId",
                                as: "leadSourceData",
                                pipeline: [{ $project: { name: 1, cost: 1, channelId: 1 } }],
                            },
                        },
                        {
                            $unwind: {
                                path: "$leadSourceData",
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                        {
                            $lookup: {
                                from: "Campaign",
                                foreignField: "_id",
                                localField: "campaignId",
                                as: "campaignData",
                                pipeline: [{ $project: { name: 1, spend: 1, leadSourceId: 1 } }],
                            },
                        },
                        {
                            $unwind: {
                                path: "$campaignData",
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                        {
                            $lookup: {
                                from: "Contact",
                                foreignField: "_id",
                                localField: "contactId",
                                as: "client",
                                pipeline: [{ $project: { fullName: 1, businessName: 1, isBusiness: 1 } }],
                            },
                        },
                        {
                            $unwind: {
                                path: "$client",
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                    ]),
                    this.companySettingModel.findOne({ companyId, deleted: false }),
                    this.checkpointModel
                        .find({
                            companyId,
                            deleted: { $ne: true },
                            stageGroup: { $in: [StageGroupEnum.Sales, StageGroupEnum.Leads] },
                        })
                        .select("_id name symbol stageGroup sequence")
                        .sort({ sequence: 1, name: 1 }),

                    this.leadsModel.aggregate([
                        {
                            $match: {
                                companyId,
                                deleted: false,
                                newLeadDate: { $gte: dateStart, $lte: dateEnd },
                            },
                        },
                        {
                            $lookup: {
                                from: "Opportunity",
                                localField: "oppId",
                                foreignField: "_id",
                                as: "opportunity",
                                pipeline: [
                                    {
                                        $lookup: {
                                            from: "LeadSource",
                                            localField: "leadSourceId",
                                            foreignField: "_id",
                                            as: "leadSourceData",
                                            pipeline: [{ $project: { name: 1, cost: 1, channelId: 1 } }],
                                        },
                                    },
                                    {
                                        $unwind: {
                                            path: "$leadSourceData",
                                            preserveNullAndEmptyArrays: true,
                                        },
                                    },
                                ],
                            },
                        },
                        {
                            $unwind: {
                                path: "$opportunity",
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                        {
                            $lookup: {
                                from: "Contact",
                                pipeline: [
                                    {
                                        $project: {
                                            _id: 1,
                                            fullName: 1,
                                            businessName: 1,
                                            isBusiness: 1,
                                        },
                                    },
                                ],
                                foreignField: "_id",
                                localField: "contactId",
                                as: "contactDetails",
                            },
                        },
                        {
                            $unwind: {
                                path: "$contactDetails",
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                        {
                            $lookup: {
                                from: "Campaign",
                                foreignField: "_id",
                                localField: "campaignId",
                                as: "campaignData",
                                pipeline: [{ $project: { name: 1, spend: 1, leadSourceId: 1 } }],
                            },
                        },
                        {
                            $unwind: {
                                path: "$campaignData",
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                        {
                            $lookup: {
                                from: "LeadSource",
                                localField: "leadSourceId", // from the lead itself
                                foreignField: "_id",
                                as: "directLeadSourceData",
                                pipeline: [{ $project: { name: 1, cost: 1, channelId: 1 } }],
                            },
                        },
                        {
                            $unwind: {
                                path: "$directLeadSourceData",
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                        {
                            $addFields: {
                                leadSourceData: {
                                    $ifNull: ["$opportunity.leadSourceData", "$directLeadSourceData"],
                                },
                                fullName: {
                                    $cond: [
                                        { $eq: ["$contactDetails.isBusiness", true] },
                                        "$contactDetails.businessName",
                                        "$contactDetails.fullName",
                                    ],
                                },

                                isUnassigned: {
                                    $cond: {
                                        if: {
                                            $and: [
                                                { $eq: ["$oppId", null] },
                                                { $eq: ["$leadSourceData", null] },
                                            ],
                                        },
                                        then: true,
                                        else: false,
                                    },
                                },
                            },
                        },
                        {
                            $match: {
                                $or: [
                                    { oppId: null },
                                    { "opportunity._id": null },
                                    {
                                        // Opportunity is outside the reporting range
                                        $or: [
                                            { "opportunity.oppDate": { $lt: dateStart } },
                                            { "opportunity.oppDate": { $gt: dateEnd } },
                                        ],
                                    },
                                ],
                            },
                        },
                        {
                            $project: {
                                contactId: 1,
                                fullName: 1,
                                leadSourceData: 1,
                                opportunity: 1,
                                newLeadDate: 1,
                                oppId: 1,
                                oppDate: 1,
                                _id: 1,
                                status: 1,
                                leadSourceId: 1,
                                campaignId: 1,
                                campaignData: 1,
                            },
                        },
                    ]),
                ]);

            for (let i = 0; i < opps.length; i++) {
                const opp: any = opps[i];
                const order: any = opp?.order;

                opp.jobDone = this.jobDoneIndicator(order);
                const oppId = opp._id;
                // @TODO : projectPrice will always be available
                if (!order || !opp.acceptedProjectId || !opp?.projectPrice) continue;

                if (opp.jobDone) {
                    const oppComm = await this.commissionModificationModel.find({ oppId, companyId });

                    const {
                        actualLaborCost = 0,
                        actualPrice = 0,
                        actualMatCost = 0,
                        subcontractorCost = 0,
                    } = order?.actualTotals || {};
                    // budget
                    const budget = {
                        total: actualPrice,
                    };
                    opp.budget = budget;

                    const laborBurden = actualLaborCost * coVar.ttlBurden;
                    const modifiedCommission = roundTo2(
                        order?.priceTotals?.commission +
                            oppComm.reduce((sum, item) => {
                                return sum + (item?.amount || 0);
                            }, 0),
                    );

                    // actual gross profit
                    const labor = actualLaborCost + laborBurden + subcontractorCost;
                    const grossProfit = actualPrice - modifiedCommission - actualMatCost - labor;
                    opp.grossProfit = roundTo2(grossProfit);
                    delete opp.projectPrice;
                } else {
                    const [crewReport, logs] = await Promise.all([
                        this.crewProjectReport(companyId, oppId),
                        this.getDailylogFroQuery({
                            projects: {
                                $elemMatch: {
                                    oppId,
                                },
                            },
                            deleted: { $ne: true },
                        }),
                    ]);
                    const { allWorkOrders } = crewReport;

                    const price = order?.priceTotals;
                    const projectPrice = opp?.projectPrice;

                    // logs
                    const logTotals = {
                        roofSq: 0,
                        tearSq: 0,
                        plywood: 0,
                        hours: 0,
                        matCosts: 0,
                    };

                    logs.map((log) => {
                        const opp = log.projects.find((project) => {
                            return project.oppId === oppId;
                        });

                        logTotals.roofSq += roundTo2(opp?.roofingDone || opp?.roofingSQ || 0);
                        logTotals.tearSq += roundTo2(opp.tearOffDone || opp?.tearOffSQ || 0);
                        logTotals.plywood += roundTo2(opp.plywoodReplaced || opp?.instSheet || 0);
                        logTotals.hours += roundTo2(opp.manHours || opp?.addManHours || 0);
                        logTotals.matCosts += roundTo2(opp.materialCosts || opp?.addMaterials || 0);
                    });

                    const { newTotal, materialBudget, laborBudget } = this.calculateProjectVolume(
                        opp?.changeOrderValue,
                        opp.soldValue,
                        allWorkOrders,
                        price,
                        projectPrice.variables,
                        opp?.changeOrders,
                        order?.modifiedBudget,
                        logTotals,
                    );
                    // budget
                    const budget = {
                        total: newTotal,
                    };
                    opp.budget = budget;

                    // estimated gross profit
                    const grossProfit = newTotal - (materialBudget + laborBudget + (price?.commission || 0));
                    opp.grossProfit = roundTo2(grossProfit);
                    delete opp.projectPrice;
                }
            }

            const oppsFromAllLeads = allLeads.map((lead) => lead.opportunity).filter((opp) => opp);
           
            getActualCostBetweenDates(allLeadSource, dateStart, dateEnd);
            getActualCostBetweenDates(allCampaigns, dateStart, dateEnd);

            // Organize data by channels
            const channels = this.organizeChannelData(allChannels, allLeadSource, allCampaigns, [
                ...opps,
                ...allLeads,
            ]);
            const campaign = this.organizeCampaignData(allCampaigns, [...opps, ...allLeads]);
            const leadSource = this.organizeLeadSourceData(allLeadSource, campaign, [...opps, ...allLeads]);

            // Process with checkpoints
            this.processSourceWithCheckpoints(
                leadSource,
                checkpoints,
                [...opps, ...allLeads],
                dateStart,
                dateEnd,
            );
            this.processSourceWithCheckpoints(
                campaign,
                checkpoints,
                [...opps, ...allLeads],
                dateStart,
                dateEnd,
            );
            this.processSourceWithCheckpoints(
                channels,
                checkpoints,
                [...opps, ...allLeads],
                dateStart,
                dateEnd,
            );

            this.calculateBudgetAndProfit(leadSource, opps, dateStart, dateEnd);
            this.calculateBudgetAndProfit(campaign, opps, dateStart, dateEnd);
            this.calculateBudgetAndProfit(channels, opps, dateStart, dateEnd);

            // Format opportunities for response
            const filteredOpps = this.formatOpportunities([...opps, ...allLeads]);

            // const report: any = {
            //     leadSource,
            //     opps: filteredOpps,
            //     campaign,
            //     channels,
            //     allLeads,
            //     oppsFromAllLeads,
            // };
            const report: any = {
                leadSource,
                opps: filteredOpps,
                campaign,
                channels,
            };

            return { report };
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    private organizeChannelData(allChannels, allLeadSource, allCampaigns, opps) {
        return (
            allChannels
                .map((channel) => {
                    let totalCost = 0;
                    const channelLeadSources = allLeadSource.filter(
                        (ls) => ls.channelId && ls.channelId.toString() === channel._id.toString(),
                    );
                    channelLeadSources.forEach((ls) => {
                        totalCost += ls.totalCost || 0;
                    });
                    const channelCampaigns = allCampaigns.filter(
                        (camp) =>
                            camp.leadSourceId &&
                            channelLeadSources.some(
                                (ls) => ls._id.toString() === camp.leadSourceId.toString(),
                            ),
                    );
                    channelCampaigns.forEach((camp) => {
                        totalCost += camp.totalCost || 0;
                    });

                    const oppIds = opps
                        .filter(
                            (opp) =>
                                opp.leadSourceData &&
                                (channelLeadSources.some(
                                    (ls) => ls._id.toString() === opp.leadSourceData._id.toString(),
                                ) ||
                                    (opp.campaignData &&
                                        channelCampaigns.some(
                                            (camp) => camp._id.toString() === opp.campaignData._id.toString(),
                                        ))),
                        )
                        .map((opp) => opp._id);

                    return {
                        _id: channel._id,
                        name: channel.name,
                        channelId: channel._id,
                        order: channel.order,
                        totalOpps: oppIds.length,
                        totalCost,
                        oppIds,
                    };
                })
                // Filter out channels where both totalCost and totalOpps are 0
                .filter((channel) => channel.totalCost > 0 || channel.totalOpps > 0)
        );
    }

    private organizeLeadSourceData(allLeadSource, processedCampaigns, opps) {
        const organized = allLeadSource
            .map((ls) => {
                // Find opportunities/leads associated with this lead source
                const oppIds = opps
                    .filter(
                        (opp) =>
                            opp.leadSourceData &&
                            opp.leadSourceData._id &&
                            opp.leadSourceData._id.toString() === ls._id.toString(),
                    )
                    .map((opp) => opp._id);

                // Find campaigns associated with this lead source
                const relatedCampaigns = processedCampaigns.filter(
                    (camp) => camp.leadSourceId && camp.leadSourceId.toString() === ls._id.toString(),
                );

                // Calculate total cost (lead source cost + all campaign costs)
                let totalCost = ls.totalCost || 0;
                relatedCampaigns.forEach((camp) => {
                    totalCost += camp.totalCost || 0;
                });

                return {
                    _id: ls._id,
                    name: ls.name,
                    channelId: ls.channelId,
                    order: ls.order,
                    totalOpps: oppIds.length,
                    oppIds,
                    totalCost,
                };
            })
            .filter((ls) => ls.totalCost > 0 || ls.totalOpps > 0);

        const unassignedOpps = opps.filter((opp) => !opp.leadSourceData || !opp.leadSourceData._id);
        if (unassignedOpps.length > 0) {
            organized.push({
                _id: null,
                name: "Unassigned",
                channelId: null,
                order: null,
                totalOpps: unassignedOpps.length,
                oppIds: unassignedOpps.map((opp) => opp._id),
                totalCost: 0,
            });
        }

        return organized;
    }

    private organizeCampaignData(allCampaigns, opps) {
        return (
            allCampaigns
                .map((camp) => {
                    const oppIds = opps
                        .filter(
                            (opp) =>
                                opp.campaignData && opp.campaignData._id.toString() === camp._id.toString(),
                        )
                        .map((opp) => opp._id);
                    let totalCost = 0;
                    totalCost += camp.totalCost || 0;

                    return {
                        _id: camp._id,
                        campaignId: camp._id,
                        name: camp.name,
                        leadSourceId: camp.leadSourceId,
                        order: camp.order,
                        totalCost,
                        totalOpps: oppIds.length,
                        oppIds,
                    };
                })
                // Filter out campaigns where both totalCost and totalOpps are 0
                .filter((camp) => camp.totalCost > 0 || camp.totalOpps > 0)
        );
    }

    private formatOpportunities(opps) {
        return opps.map((opp) => {
            const formattedOpp: any = {
                _id: opp._id,
                oppType: opp.oppType,
                contactId: opp.contactId,
                contactName: opp?.client?.isBusiness ? opp?.client?.businessName : opp?.client?.fullName,
                PO: opp.PO,
                num: opp.num,
                acceptedType: opp.acceptedType,
                acceptedProjectDetails: opp.acceptedProjectDetails?.name,
                total: opp.total,
                newLeadDate: opp.newLeadDate,
                oppDate: opp.oppDate,
                needsAssessmentDate: opp.needsAssessmentDate,
                presentationDate: opp.presentationDate,
                saleDate: opp.saleDate,
                leadSource: opp?.leadSourceData?.name,
                campaign: opp?.campaignData?.name,
                stageGroup: opp?.stageData?.stageGroup,
                fullName: opp?.fullName || "",
                jobDone: opp?.jobDone,
            };
            
            // Only include budget and grossProfit if there's a sale date
            if (opp.saleDate) {
                formattedOpp.budget = opp.budget;
                formattedOpp.grossProfit = opp.grossProfit;
            }
            
            return formattedOpp;
        });
    }

    private calculateBudgetAndProfit(sourceArray, opps, start: Date, end: Date) {
        for (const source of sourceArray) {
            let totalBudget = 0;
            let totalGrossProfit = 0;

            for (const oppId of source.oppIds) {
                const fullOpp = opps.find(
                    (o) => o._id === oppId && new Date(o.saleDate) >= start && new Date(o.saleDate) <= end,
                );
                if (!fullOpp) continue;

                totalBudget += fullOpp?.budget?.total || 0;
                totalGrossProfit += fullOpp?.grossProfit || 0;
            }

            source.totalBudget = roundTo2(totalBudget);
            source.totalGrossProfit = roundTo2(totalGrossProfit);
        }
    }

    private checkpointAndConversion(checkpoints, opps, start: Date, end: Date) {
        const report: any = {};
        report.checkpointCounts = {};
        report.groupedConversion = {};
        const validLeads = opps.filter(
            (opp) => opp.newLeadDate >= start && opp.newLeadDate <= end && opp.status !== "invalid",
        );

        report.checkpointCounts["Valid Leads"] = {
            count: validLeads.length,
            opps: validLeads.map((opp) => opp._id),
        };

        for (const checkpoint of checkpoints) {
            const { symbol, name } = checkpoint;
            const matchedOpps = opps
                .filter((opp) => new Date(opp[symbol]) >= start && new Date(opp[symbol]) <= end)
                .map((opp) => opp._id);
            const count = matchedOpps.length;
            report.checkpointCounts[name] = { count, opps: matchedOpps };
            checkpoint[name] = count;
        }

        for (let i = 0; i < checkpoints.length - 1; i++) {
            const from = checkpoints[i].name;
            const fromCount = checkpoints[i][from];

            for (let j = i + 1; j < checkpoints.length; j++) {
                const to = checkpoints[j].name;
                const toCount = checkpoints[j][to];

                const label = `${from} > ${to}`;
                const percent = fromCount > 0 ? roundTo2((100 * toCount) / fromCount) : 0;

                if (!report.groupedConversion[from]) {
                    report.groupedConversion[from] = {};
                }

                report.groupedConversion[from][label] = percent;
            }
        }

        return report;
    }
    private processSourceWithCheckpoints(sourceArray, checkpoints, opps, start: Date, end: Date) {
        for (const source of sourceArray) {
            const seen = new Set();
            const matchedOpps = opps.filter((opp) => {
                const id = opp._id;
                if (source.oppIds.includes(id) && !seen.has(id)) {
                    seen.add(id);
                    return true;
                }
                return false;
            });

            const { checkpointCounts, groupedConversion } = this.checkpointAndConversion(
                checkpoints,
                matchedOpps,
                start,
                end,
            );

            source.checkpointCounts = checkpointCounts;
            source.conversion = groupedConversion;
        }
    }

    oppByStageCount(opps, allStage, salesPersonId, dateStart, dateEnd) {
        const lostStagesOpp = [];

        for (const s of allStage) {
            // skipping operations stages
            if (s.stageGroup === StageGroupEnum.Operations) continue;

            const data = {
                name: s.name,
                sequence: s.sequence,
                stageGroup: s.stageGroup,
                totalNum: 0,
                opps: [],
            };

            data.opps = opps.filter(
                (o) =>
                    o.stage === s._id &&
                    new Date(o?.lostDate) >= dateStart &&
                    new Date(o?.lostDate) <= dateEnd &&
                    salesPersonId.includes(o.salesPerson),
            );
            // Deleting fields that are not needed
            data.opps.forEach((o) => {
                // delete o.checkpointActivity;
                delete o.salesPersonHistory;
                delete o.dateReceived;
                delete o.todoCheck;
                delete o.distance;
                delete o.updatedAt;
                delete o.nextAction;
                delete o.actions;
            });
            data.totalNum = data.opps.length;

            lostStagesOpp.push(data);
        }

        return lostStagesOpp;
    }

    //TODO: checkpointActivity to be used everywhere
    //Conversion Rates
    async conversionReport(userId: string, companyId: string, start: Date, end: Date, salesPersonId: string) {
        try {
            const convRates = [];
            const currDate = new Date();
            // Check if they are within 12 hours of each other
            const hoursInMilliseconds = 12 * 60 * 60 * 1000;
            const salesPersonArr = salesPersonId.split(",");

            const [leadTypes, opps] = await Promise.all([
                this.projectTypeModel.find({ companyId, deleted: false }).select("_id name typeReplacement"),
                this.opportunityModel.aggregate([
                    {
                        $match: {
                            companyId,
                            salesPerson: { $in: salesPersonArr },
                            deleted: { $ne: true },
                            // status: { $ne: "inactive" },
                            warrantyType: { $ne: true },
                            oppDate: {
                                $gte: start,
                                $lte: end,
                            },
                            // status: { $ne: OpportunityStatusEnum.Lost },
                        },
                    },
                    {
                        $set: {
                            oppType: {
                                $cond: {
                                    if: {
                                        $and: [
                                            { $gt: ["$saleDate", null] },
                                            { $gt: ["$acceptedType", null] },
                                        ],
                                    },
                                    then: "$acceptedType",
                                    else: "$oppType",
                                },
                            },
                        },
                    },
                    {
                        $lookup: {
                            from: "Member",
                            foreignField: "_id",
                            localField: "salesPerson",
                            as: "salesPersonData",
                            pipeline: [
                                {
                                    $project: {
                                        _id: 1,
                                        name: 1,
                                    },
                                },
                            ],
                        },
                    },
                    {
                        $unwind: {
                            path: "$salesPersonData",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "ProjectType",
                            foreignField: "_id",
                            localField: "oppType",
                            as: "oppType",
                            pipeline: [
                                {
                                    $project: {
                                        _id: 1,
                                        name: 1,
                                        typeReplacement: 1,
                                    },
                                },
                            ],
                        },
                    },
                    {
                        $unwind: {
                            path: "$oppType",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Contact",
                            pipeline: [
                                {
                                    $project: {
                                        _id: 1,
                                        fullName: 1,
                                        businessName: 1,
                                        isBusiness: 1,
                                    },
                                },
                            ],
                            foreignField: "_id",
                            localField: "contactId",
                            as: "contactId",
                        },
                    },
                    {
                        $unwind: {
                            path: "$contactId",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "LeadSource",
                            localField: "leadSourceId",
                            foreignField: "_id",
                            pipeline: [{ $project: { name: 1 } }],
                            as: "leadSourceObj",
                        },
                    },
                    {
                        $addFields: {
                            fullName: "$contactId.fullName",
                            businessName: "$contactId.businessName",
                            leadSource: { $arrayElemAt: ["$leadSourceObj.name", 0] },
                            // selfGen: { $in: ["$referredBy", salesPersonArr] },
                        },
                    },
                    {
                        $project: {
                            comments: 0,
                            activities: 0,
                            stepsChecklist: 0,
                            leadSourceObj: 0,
                            taxJurisdiction: 0,
                            assignedTo: 0,
                            oppNotes: 0,
                            // status: 0,
                            type: 0,
                            workingCrew: 0,
                            acceptedType: 0,
                            acceptedProjectId: 0,
                            discount: 0,
                            editedBy: 0,
                            duration: 0,
                            companyAddress: 0,
                            companyLat: 0,
                            companyLang: 0,
                            street: 0,
                            // city: 0,
                            zip: 0,
                            __v: 0,
                            deleted: 0,
                            createdBy: 0,
                        },
                    },
                    { $sort: { createdAt: -1 } },
                ]),
            ]);

            const salesPersonCount = (data: any[], leadOpps: Record<string, number>, sold: boolean) => {
                if (salesPersonArr.length <= 1) return undefined;

                const initialObj = sold ? { count: 0, sold: 0, percent: 0 } : { count: 0, percent: 0 };

                return data.reduce((acc: Record<string, typeof initialObj>, opp) => {
                    const name = opp.salesPersonData.name;
                    const soldValue = sold ? opp.soldValue ?? 0 : 0;

                    if (!acc[name]) {
                        acc[name] = { ...initialObj };
                    }

                    acc[name].count += 1;
                    if (sold) acc[name].sold += soldValue;
                    acc[name].percent = roundTo1((acc[name].count / leadOpps[name]) * 100) || 0;

                    return acc;
                }, {});
            };

            for (const type of leadTypes) {
                /*
                Opportunity -> oppDate
                All opps that have a Opportunity date within the date range
                This report will follow these leads through their lifecycle, and only these leads.
                */
                const lTypeOpps = opps.filter((o) => o.oppType._id === type._id);

                const res: any = {
                    id: type.id,
                    name: type.name,
                    typeReplacement: type.typeReplacement,
                    allOpps: lTypeOpps,
                    checkpoints: [],
                };
                //all opps

                /*
                  NA Scheduled -> needsAssessmentDate
                1. Opportunity > Needs Assessment
                    Sch All opps that have a oppDate AND NA Date
                    NA Date can be in past or future
                    Avg time between, High, Low

                2.Needs Assessment Drop Off
                    All opps that have a oppDate but NO NA Date

                */
                const NAScheduleData = lTypeOpps.filter(
                    (o) =>
                        o?.checkpointActivity?.oppDate?.created &&
                        o?.checkpointActivity?.needsAssessmentDate?.created,
                );
                const dayCalc1 = this.oppDayCal(NAScheduleData, "oppDate", "needsAssessmentDate");

                const NADropOffData = lTypeOpps.filter(
                    (o) =>
                        o?.checkpointActivity?.oppDate?.created &&
                        !o?.checkpointActivity?.needsAssessmentDate?.created,
                );
                const leadOppsPerson = lTypeOpps.reduce((acc, opp) => {
                    const name = opp.salesPersonData.name;
                    if (acc[name]) {
                        acc[name] += 1;
                    } else {
                        acc[name] = 1;
                    }
                    return acc;
                }, {});

                res.checkpoints.push({
                    name: "NA Scheduled",
                    count: NAScheduleData.length,
                    percent: roundTo1((NAScheduleData.length / lTypeOpps.length) * 100),
                    sequence: 2,
                    salesPerson: salesPersonCount(NAScheduleData, leadOppsPerson, false),
                    breakdown: [
                        {
                            name: "Opportunity > NA Scheduled",
                            count: NAScheduleData.length,
                            percent: roundTo1((NAScheduleData.length / lTypeOpps.length) * 100),
                            countOf: lTypeOpps.length,
                            high: dayCalc1.high,
                            avg: Math.ceil(dayCalc1.avg / NAScheduleData.length) || 0,
                            low: dayCalc1.low,
                            oppList: NAScheduleData.map((o) => o._id),
                            salesPerson: salesPersonCount(NAScheduleData, leadOppsPerson, false),
                        },
                        {
                            name: "Needs Assessment Drop Off",
                            count: NADropOffData.length,
                            percent: roundTo1((NADropOffData.length / lTypeOpps.length) * 100),
                            countOf: lTypeOpps.length,
                            oppList: NADropOffData.map((o) => o._id),
                            salesPerson: salesPersonCount(NADropOffData, leadOppsPerson, false),
                        },
                    ],
                });
                /*
                NA Kept -> needsAssessmentDate
                1. Needs Assessment Sch > Kept
                    All these opps that have a NA Date in the past
                    Avg time between, High, Low

                2. Needs Assessment Cancels
                    All these opps that have a NA Date ithat was created, but then removed

                3. Needs Assessment Pending
                    All these opps that have a NA Date ithat is in the future.
                    Ex. Current date is 5/25/24, Opportunity date in report date range, and NA Date is on 5/27/24

                */

                const NAKeptData = NAScheduleData.filter(
                    (o) =>
                        !o?.checkpointActivity?.needsAssessmentDate?.deleted &&
                        new Date(o?.checkpointActivity?.needsAssessmentDate?.created) < currDate,
                );
                const dayCalc2 = this.oppDayCal(NAKeptData, "oppDate", "needsAssessmentDate");

                const NACancelData = NAScheduleData.filter(
                    (o) =>
                        o?.checkpointActivity?.needsAssessmentDate?.created &&
                        o?.checkpointActivity?.needsAssessmentDate?.deleted,
                );
                const NAPendingData = NAScheduleData.filter(
                    (o) =>
                        !o?.checkpointActivity?.needsAssessmentDate?.deleted &&
                        new Date(o?.checkpointActivity?.needsAssessmentDate?.created) > currDate,
                );

                const NASchedulePerson = NAScheduleData.reduce((acc, opp) => {
                    const name = opp.salesPersonData.name;
                    if (acc[name]) {
                        acc[name] += 1;
                    } else {
                        acc[name] = 1;
                    }
                    return acc;
                }, {});

                res.checkpoints.push({
                    name: "NA Kept",
                    count: NAKeptData.length, //TODO
                    percent: roundTo1((NAKeptData.length / NAScheduleData.length) * 100), //TODO
                    sequence: 3,
                    salesPerson: salesPersonCount(NAKeptData, NASchedulePerson, false),
                    breakdown: [
                        {
                            name: "NA Scheduled > NA Kept",
                            count: NAKeptData.length,
                            percent: roundTo1((NAKeptData.length / NAScheduleData.length) * 100),
                            countOf: NAScheduleData.length,
                            high: dayCalc2.high,
                            avg: Math.ceil(dayCalc2.avg / NAKeptData.length) || 0,
                            low: dayCalc2.low,
                            oppList: NAKeptData.map((o) => o._id),
                            salesPerson: salesPersonCount(NAKeptData, NASchedulePerson, false),
                        },
                        {
                            name: "Needs Assessment Cancels",
                            count: NACancelData.length,
                            countOf: NAScheduleData.length,
                            percent: roundTo1((NACancelData.length / NAScheduleData.length) * 100),
                            oppList: NACancelData.map((o) => o._id),
                            salesPerson: salesPersonCount(NACancelData, NASchedulePerson, false),
                        },
                        {
                            name: "Needs Assessment Pending",
                            count: NAPendingData.length,
                            countOf: NAScheduleData.length,
                            percent: roundTo1((NAPendingData.length / NAScheduleData.length) * 100),
                            oppList: NAPendingData.map((o) => o._id),
                            salesPerson: salesPersonCount(NAPendingData, NASchedulePerson, false),
                        },
                    ],
                });
                /*
                Price Presented -> presentationDate
                1. NA Kept > Price Given
                    NA Date = Pres Date (within 6 hours of each other)
                    Both NA & Pres date must be in the past from current date
                    Opps from initial set of leads that a NA appt was already attended, and at that appt the presentation was done as well.

                2.NA Kept > No Price Given
                    NA Date != Pres Date OR
                    No Pres Date
                    NA date must in past
                    Pres date can be in future
                    Opps from initial set of leads that a NA appt was already attended, but the presentation was NOT done at that appt.

                3.No Price Given > 2nd Appt kept
                    NA Date & Pres Date both exist
                    NA Date in past
                    Pres Date in past
                    Avg time between, High, Low
                    Opps from initial set of leads that a NA appt was already attended, the the presentation was NOT done at that appt, AND the presentation already happened at the followup appt

                */

                const priceGivenData = NAKeptData.filter((o) => {
                    const needsAssessmentDate = new Date(o?.checkpointActivity?.needsAssessmentDate?.created);
                    const presentationDate = new Date(o?.checkpointActivity?.presentationDate?.created);

                    // Check if both dates are in the past
                    if (needsAssessmentDate < currDate && presentationDate < currDate) {
                        const timeDifference = Math.abs(
                            needsAssessmentDate.getTime() - presentationDate.getTime(),
                        );

                        return timeDifference <= hoursInMilliseconds;
                    }

                    return false;
                });

                const noPriceGivenData = NAKeptData.filter((o) => {
                    const needsAssessmentDate = new Date(
                        o?.checkpointActivity?.needsAssessmentDate?.created,
                    ).getTime();

                    // if pres date not added
                    if (!o?.checkpointActivity?.presentationDate?.created) return true;

                    const presentationDate = new Date(
                        o?.checkpointActivity?.presentationDate?.created,
                    ).getTime();

                    // needsAssessmentDate must be in the past
                    if (needsAssessmentDate >= currDate.getTime()) {
                        return false;
                    }

                    // Check if presentationDate is within 6 hours of needsAssessmentDate, or if there's no presentationDate
                    if (
                        !presentationDate ||
                        Math.abs(needsAssessmentDate - presentationDate) > hoursInMilliseconds
                    ) {
                        return true;
                    }

                    return false;
                });

                const secondApptData = noPriceGivenData.filter((o) => {
                    const needsAssessmentDate = new Date(
                        o?.checkpointActivity?.needsAssessmentDate?.created,
                    ).getTime();
                    const presentationDate = new Date(
                        o?.checkpointActivity?.presentationDate?.created,
                    ).getTime();

                    // Check if both dates exist and are in the past
                    return needsAssessmentDate < currDate.getTime() && presentationDate < currDate.getTime();
                });
                const dayCalc3 = this.oppDayCal(secondApptData, "needsAssessmentDate", "presentationDate");

                const noSecondApptData = noPriceGivenData.filter((o) => {
                    return !o?.checkpointActivity?.presentationDate?.created;
                });
                const pricePresented = [...priceGivenData, ...secondApptData];
                const NAKeptPerson = NAKeptData.reduce((acc, opp) => {
                    const name = opp.salesPersonData.name;
                    if (acc[name]) {
                        acc[name] += 1;
                    } else {
                        acc[name] = 1;
                    }
                    return acc;
                }, {});

                const noPriceGivenPerson = noPriceGivenData.reduce((acc, opp) => {
                    const name = opp.salesPersonData.name;
                    if (acc[name]) {
                        acc[name] += 1;
                    } else {
                        acc[name] = 1;
                    }
                    return acc;
                }, {});

                res.checkpoints.push({
                    name: "Price Presented",
                    count: pricePresented.length,
                    percent: roundTo1((pricePresented.length / NAKeptData.length) * 100),
                    sequence: 4,
                    salesPerson: salesPersonCount(pricePresented, NAKeptPerson, false),
                    breakdown: [
                        {
                            name: "NA Kept > Price Given",
                            count: priceGivenData.length,
                            countOf: NAKeptData.length,
                            percent: roundTo1((priceGivenData.length / NAKeptData.length) * 100),
                            oppList: priceGivenData.map((o) => o._id),
                            salesPerson: salesPersonCount(priceGivenData, NAKeptPerson, false),
                        },
                        {
                            name: "NA Kept > No Price Given",
                            count: noPriceGivenData.length,
                            countOf: NAKeptData.length,
                            percent: roundTo1((noPriceGivenData.length / NAKeptData.length) * 100),
                            oppList: noPriceGivenData.map((o) => o._id),
                            salesPerson: salesPersonCount(noPriceGivenData, NAKeptPerson, false),
                        },
                        {
                            name: "No Price Given > 2nd Appt kept",
                            count: secondApptData.length,
                            countOf: noPriceGivenData.length,
                            percent: roundTo1((secondApptData.length / noPriceGivenData.length) * 100),
                            oppList: secondApptData.map((o) => o._id),
                            high: dayCalc3.high,
                            avg: Math.ceil(dayCalc3.avg / secondApptData.length) || 0,
                            low: dayCalc3.low,
                            salesPerson: salesPersonCount(secondApptData, noPriceGivenPerson, false),
                        },
                        {
                            name: "No Price Given > No 2nd Appt",
                            count: noSecondApptData.length,
                            countOf: noPriceGivenData.length,
                            percent: roundTo1((noSecondApptData.length / noPriceGivenData.length) * 100),
                            oppList: noSecondApptData.map((o) => o._id),
                            salesPerson: salesPersonCount(noSecondApptData, noPriceGivenPerson, false),
                        },
                    ],
                });
                /*
                Sold -> saleDate
                1. Price Given > Closed 1 stop
                    NA Date = Pres Date = Sale Date
                    All dates must be in past
                    (within 6 hours)
                    Opps from initial set of leads that:
                        a NA appt was already attended,
                        at that appt the presentation was done,
                        the job was sold AT that appt.

                2.Price Given > Closed in followup
                    NA Date = Pres Date != Sale Date
                    All dates must be in past
                    Avg time between, High, Low

                3.Price Given > Not closed
                    NA Date = Pres Date & no Sale Date
                    NA Date and Pres Date has to be in past

                4.2nd Appt > Closed at table
                    NA Date != Pres Date = Sale Date
                    All dates must be in past
                    (within 6 hours)

                5.2nd Appt > Closed in followup
                    NA Date != Pres Date != Sale Date
                    All dates must be in past
                    Avg time between, High, Low

                6.2nd Appt > Not closed
                    NA Date != Pres Date & no Sale Date
                    NA Date and Pres Date has to be in past

                */

                const soldOneStopData = priceGivenData.filter((o) => {
                    // const needsAssessmentDate = new Date(
                    //     o?.checkpointActivity?.needsAssessmentDate?.created,
                    // );
                    const presentationDate = new Date(o?.checkpointActivity?.presentationDate?.created);
                    const saleDate = new Date(o?.checkpointActivity?.saleDate?.created);

                    // if (o.status !== OpportunityStatusEnum.Active) return false;
                    // Check if both dates are in the past
                    if (saleDate < currDate) {
                        const timeDifference = Math.abs(presentationDate.getTime() - saleDate.getTime());

                        return timeDifference <= hoursInMilliseconds;
                    }

                    return false;
                });

                const soldFolloupData = priceGivenData.filter((o) => {
                    const needsAssessmentDate = new Date(o?.checkpointActivity?.needsAssessmentDate?.created);
                    const presentationDate = new Date(o?.checkpointActivity?.presentationDate?.created);
                    const saleDate = new Date(o?.checkpointActivity?.saleDate?.created);

                    if (o.status !== OpportunityStatusEnum.Active) return false;

                    // Check if both dates are in the past
                    if (saleDate < currDate) {
                        const timeDifference1 = Math.abs(
                            needsAssessmentDate.getTime() - presentationDate.getTime(),
                        );

                        const timeDifference2 = Math.abs(presentationDate.getTime() - saleDate.getTime());

                        return (
                            timeDifference1 <= hoursInMilliseconds && timeDifference2 > hoursInMilliseconds
                        );
                    }

                    return false;
                });

                const dayCalc4 = this.oppDayCal(soldFolloupData, "presentationDate", "saleDate");

                const notSoldData = priceGivenData.filter((o) => {
                    const needsAssessmentDate = new Date(o?.checkpointActivity?.needsAssessmentDate?.created);
                    const presentationDate = new Date(o?.checkpointActivity?.presentationDate?.created);
                    // if (o.status !== OpportunityStatusEnum.Active) return false;
                    // const saleDate = new Date(
                    //     o?.checkpointActivity?.saleDate?.created,
                    // );

                    // Check if both dates are in the past
                    if (!o.saleDate) {
                        const timeDifference1 = Math.abs(
                            needsAssessmentDate.getTime() - presentationDate.getTime(),
                        );

                        return timeDifference1 <= hoursInMilliseconds;
                    }

                    return false;
                });

                const soldSecondAptData = secondApptData.filter((o) => {
                    const needsAssessmentDate = new Date(o?.checkpointActivity?.needsAssessmentDate?.created);
                    const presentationDate = new Date(o?.checkpointActivity?.presentationDate?.created);
                    const saleDate = new Date(o?.checkpointActivity?.saleDate?.created);
                    // if (o.status !== OpportunityStatusEnum.Active) return false;

                    // Check if both dates are in the past
                    if (o?.saleDate && saleDate < currDate) {
                        const timeDifference1 = Math.abs(
                            needsAssessmentDate.getTime() - presentationDate.getTime(),
                        );
                        const timeDifference2 = Math.abs(presentationDate.getTime() - saleDate.getTime());

                        return (
                            timeDifference1 > hoursInMilliseconds && timeDifference2 <= hoursInMilliseconds
                        );
                    }

                    return false;
                });
                const dayCalc5 = this.oppDayCal(soldSecondAptData, "presentationDate", "saleDate");

                const soldFollowAptData = secondApptData.filter((o) => {
                    const needsAssessmentDate = new Date(o?.checkpointActivity?.needsAssessmentDate?.created);
                    const presentationDate = new Date(o?.checkpointActivity?.presentationDate?.created);
                    const saleDate = new Date(o?.checkpointActivity?.saleDate?.created);
                    // if (o.status !== OpportunityStatusEnum.Active) return false;

                    // Check if both dates are in the past
                    if (o.saleDate && saleDate < currDate) {
                        const timeDifference1 = Math.abs(
                            needsAssessmentDate.getTime() - presentationDate.getTime(),
                        );
                        const timeDifference2 = Math.abs(presentationDate.getTime() - saleDate.getTime());

                        return timeDifference1 > hoursInMilliseconds && timeDifference2 > hoursInMilliseconds;
                    }

                    return false;
                });
                const notSoldSecondAptData = secondApptData.filter((o) => {
                    const needsAssessmentDate = new Date(o?.checkpointActivity?.needsAssessmentDate?.created);
                    const presentationDate = new Date(o?.checkpointActivity?.presentationDate?.created);
                    // if (o.status !== OpportunityStatusEnum.Active) return false;

                    // Check if both dates are in the past
                    if (!o.saleDate) {
                        const timeDifference1 = Math.abs(
                            needsAssessmentDate.getTime() - presentationDate.getTime(),
                        );

                        return timeDifference1 > hoursInMilliseconds;
                    }

                    return false;
                });
                const totalCount =
                    soldOneStopData.length +
                    soldFolloupData.length +
                    soldSecondAptData.length +
                    soldFollowAptData.length;

                const soldAll = [
                    ...soldOneStopData,
                    ...soldFolloupData,
                    ...soldSecondAptData,
                    ...soldFollowAptData,
                ];

                const totalSoldValue = Math.round(
                    soldAll.reduce((sum, item) => {
                        // if (item?.status !== OpportunityStatusEnum.Active) return sum;
                        return sum + (item?.soldValue || 0);
                    }, 0),
                );

                const pricePresentedPerson = pricePresented.reduce((acc, opp) => {
                    const name = opp.salesPersonData.name;
                    if (acc[name]) {
                        acc[name] += 1;
                    } else {
                        acc[name] = 1;
                    }
                    return acc;
                }, {});
                const priceGivenPerson = priceGivenData.reduce((acc, opp) => {
                    const name = opp.salesPersonData.name;
                    if (acc[name]) {
                        acc[name] += 1;
                    } else {
                        acc[name] = 1;
                    }
                    return acc;
                }, {});
                const secondApptPerson = secondApptData.reduce((acc, opp) => {
                    const name = opp.salesPersonData.name;
                    if (acc[name]) {
                        acc[name] += 1;
                    } else {
                        acc[name] = 1;
                    }
                    return acc;
                }, {});

                //sold data
                res.checkpoints.push({
                    name: "Sold",
                    count: totalCount,
                    percent: roundTo1((totalCount / pricePresented.length) * 100),
                    sequence: 5,
                    soldValue: totalSoldValue,
                    salesPerson: salesPersonCount(soldAll, pricePresentedPerson, true),
                    breakdown: [
                        {
                            name: "Price Given > Closed 1 stop",
                            count: soldOneStopData.length,
                            countOf: priceGivenData.length,
                            percent: roundTo1((soldOneStopData.length / priceGivenData.length) * 100),
                            soldValue: Math.round(
                                soldOneStopData.reduce((sum, item) => {
                                    return sum + (item?.soldValue || 0);
                                }, 0),
                            ),
                            oppList: soldOneStopData.map((o) => o._id),
                            salesPerson: salesPersonCount(soldOneStopData, priceGivenPerson, true),
                        },
                        {
                            name: "Price Given > Closed in followup",
                            count: soldFolloupData.length,
                            countOf: priceGivenData.length,
                            percent: roundTo1((soldFolloupData.length / priceGivenData.length) * 100),
                            oppList: soldFolloupData.map((o) => o._id),
                            soldValue: Math.round(
                                soldFolloupData.reduce((sum, item) => {
                                    return sum + (item?.soldValue || 0);
                                }, 0),
                            ),
                            high: dayCalc4.high,
                            avg: Math.ceil(dayCalc4.avg / soldFolloupData.length) || 0,
                            low: dayCalc4.low,
                            salesPerson: salesPersonCount(soldFolloupData, priceGivenPerson, true),
                        },
                        {
                            name: "Price Given > Not closed",
                            count: notSoldData.length,
                            countOf: priceGivenData.length,
                            percent: roundTo1((notSoldData.length / priceGivenData.length) * 100),
                            oppList: notSoldData.map((o) => o._id),
                            soldValue: Math.round(
                                notSoldData.reduce((sum, item) => {
                                    return sum + (item?.soldValue || 0);
                                }, 0),
                            ),
                            salesPerson: salesPersonCount(notSoldData, priceGivenPerson, true),
                        },
                        {
                            name: "2nd Appt > Closed at table",
                            count: soldSecondAptData.length,
                            countOf: secondApptData.length,
                            percent: roundTo1((soldSecondAptData.length / secondApptData.length) * 100),
                            oppList: soldSecondAptData.map((o) => o._id),
                            soldValue: Math.round(
                                soldSecondAptData.reduce((sum, item) => {
                                    return sum + (item?.soldValue || 0);
                                }, 0),
                            ),
                            salesPerson: salesPersonCount(soldSecondAptData, secondApptPerson, true),
                        },
                        {
                            name: "2nd Appt > Closed in followup",
                            count: soldFollowAptData.length,
                            countOf: secondApptData.length,
                            percent: roundTo1((soldFollowAptData.length / secondApptData.length) * 100),
                            oppList: soldFollowAptData.map((o) => o._id),
                            soldValue: Math.round(
                                soldFollowAptData.reduce((sum, item) => {
                                    return sum + (item?.soldValue || 0);
                                }, 0),
                            ),
                            high: dayCalc5.high,
                            avg: Math.ceil(dayCalc5.avg / soldFollowAptData.length) || 0,
                            low: dayCalc5.low,
                            salesPerson: salesPersonCount(soldFollowAptData, secondApptPerson, true),
                        },
                        {
                            name: "2nd Appt > Not closed",
                            count: notSoldSecondAptData.length,
                            countOf: secondApptData.length,
                            percent: roundTo1((notSoldSecondAptData.length / secondApptData.length) * 100),
                            oppList: notSoldSecondAptData.map((o) => o._id),
                            soldValue: Math.round(
                                notSoldSecondAptData.reduce((sum, item) => {
                                    return sum + (item?.soldValue || 0);
                                }, 0),
                            ),
                            salesPerson: salesPersonCount(notSoldSecondAptData, secondApptPerson, true),
                        },
                    ],
                });

                const salesPersonObj =
                    salesPersonArr.length > 1
                        ? lTypeOpps.reduce((acc, opp) => {
                              const name = opp.salesPersonData.name;
                              const soldValue =
                                  (soldAll.length &&
                                      soldAll.find((o) => {
                                          return o._id === opp._id;
                                      })?.soldValue) ||
                                  0;

                              if (acc[name]) {
                                  acc[name].count += 1;
                                  acc[name].sold += soldValue;
                              } else {
                                  acc[name] = {
                                      count: 1,
                                      sold: soldValue,
                                      perLeadSold: 0,
                                  };
                              }
                              acc[name].perLeadSold = Math.round(acc[name].sold / acc[name].count);

                              return acc;
                          }, {})
                        : undefined;
                //Opportunity data
                res.checkpoints.push({
                    name: "Opportunity",
                    count: lTypeOpps.length,
                    percent: lTypeOpps.length ? 100 : 0,
                    sequence: 1,
                    perLeadSold: Math.round(totalSoldValue / lTypeOpps.length),
                    salesPerson: salesPersonObj,
                    breakdown: [
                        {
                            name: "Opportunity",
                            count: lTypeOpps.length,
                            percent: (lTypeOpps.length / lTypeOpps.length) * 100,
                            oppList: lTypeOpps.map((o) => o._id),
                            perLeadSold: Math.round(totalSoldValue / lTypeOpps.length),
                            salesPerson: salesPersonObj,
                        },
                    ],
                });

                convRates.push(res);
            }

            return new OkResponse({ convRates });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    oppDayCal(opps: any, toCheck: string, withCheck: string) {
        const days = {
            high: 0,
            avg: 0,
            low: Infinity, // Start with Infinity for minimum calculation
        };
        let totalDays = 0;

        if (opps.length === 0) {
            // Handle case when opps is empty to avoid division by zero in avg calculation
            days.low = 0;
            return days;
        }

        for (const o of opps) {
            const date1 = new Date(o[toCheck]);
            const date2 = new Date(o[withCheck]);

            // Check if both dates are valid
            if (!isNaN(date1.getTime()) && !isNaN(date2.getTime())) {
                // Calculate the difference in time
                const timeDiff = Math.abs(date1.getTime() - date2.getTime());
                // Calculate the difference in days
                const dayDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

                // Update total sum for avg calculation
                totalDays += dayDiff;

                // Update max and min day differences
                if (dayDiff > days.high) {
                    days.high = dayDiff;
                }
                if (dayDiff < days.low) {
                    days.low = dayDiff;
                }
            }
        }

        // If no differences were found, ensure low is set to 0 (as Infinity is not a valid output)
        if (days.low === Infinity) {
            days.low = 0;
        }

        // Calculate the average day difference
        days.avg = days.high + days.low;

        return days;
    }

    async WeeklyProductionReport(
        userId: string,
        companyId: string,
        startDate: Date,
        endDate: Date,
    ): Promise<any> {
        //same as crewReport
        try {
            const reportStart = new Date(startDate);
            const reportEnd = new Date(endDate);

            const [crews, subs, allWorkTaskData, pwSettingAllData, companySetting] = await Promise.all([
                this.crewModel.aggregate([
                    {
                        $match: {
                            companyId,
                            deleted: { $ne: true },
                            startDate: { $lte: reportEnd },
                            $or: [{ endDate: { $gte: reportStart } }, { endDate: { $exists: false } }],
                        },
                    },
                    {
                        $lookup: {
                            from: "CrewMember",
                            localField: "_id",
                            foreignField: "crewId",
                            as: "members",
                        },
                    },
                    {
                        $sort: { order: 1 },
                    },
                ]),
                this.subcontractorModel.find({
                    companyId,
                    deleted: { $ne: true },
                    isActive: true,
                }),
                this.workTaskModel.find({
                    companyId,
                }),
                // this.pieceWorkSettingModel.find({
                //     companyId: companyId,
                //     deleted: false,
                //     version: "f011a5e5-6dc9-4919-b612-28b3f9039671",
                // }),
                this.pieceWorkSettingModel.find({
                    companyId: companyId,
                }),

                this.companySettingModel.findOne({ companyId }),
            ]);

            const weekTotals = {
                budget: 0,
                rr: 0,
                vol: 0,
                actual: 0,
                rrDollar: 0,
                crews: [],
                missingError: [],
            };

            // Go through each crew
            for (const crew of crews) {
                // if no crew members, skip
                if (!crew.members || crew.members.length < 1) continue;
                // Find all members during report period & put in array of memberIds
                const weekActiveMembers = crew.members
                    .filter((member) => {
                        return activeCrewMember(member, reportStart, reportEnd);
                    })
                    .map((member) => member);
                const memberIdArray = weekActiveMembers.map((mem) => mem.memberId);

                const dayReports = [];
                // Go through each day
                const loopStart = new Date(startDate);
                const loopEnd = new Date(endDate);
                for (let i = loopStart; i < loopEnd; i.setDate(i.getDate() + 1)) {
                    const start = new Date(i);
                    const end = new Date(i);
                    end.setDate(end.getDate() + 1);
                    end.setMilliseconds(end.getMilliseconds() - 1);
                    const crewDayReport: any = await this.crewDayActivity(
                        start,
                        crew._id,
                        companyId,
                        pwSettingAllData,
                        allWorkTaskData,
                        companySetting,
                    );

                    const arr = crewDayReport.projects.map((a) => a.oppPO).filter((str) => str !== "");

                    crewDayReport.POList = arr;
                    crewDayReport.date = start;
                    //adding list of missing daily-log logs for error
                    weekTotals.missingError.push(...crewDayReport.mError);
                    dayReports.push(crewDayReport);
                }

                // Define crew week report
                const weekReport: any = {
                    crewId: crew._id,
                    name: crew.name,
                    type: "crew",
                    order: crew.order,
                    hours: 0,
                    pwExtras: 0,
                    pwHourly: 0,
                    pwSqs: 0,
                    pwTotal: 0,
                    travel: 0,
                    rr: 0,
                    vol: 0,
                    budget: 0,
                    members: [],
                    projects: [],
                    dayReports: dayReports,
                    baseWage: 0,
                };

                // Consolidate days stats
                for (const day of dayReports) {
                    weekReport.hours += roundTo2(day.hours);
                    weekReport.baseWage += day.baseWage;
                    weekReport.pwExtras += day.pwExtras;
                    weekReport.pwHourly += day.pwHourly;
                    weekReport.pwSqs += day.pwSqs;
                    weekReport.pwTotal += roundTo2(day.pwTotal);
                    weekReport.travel += roundTo2(day.travel);
                    weekReport.rr += roundTo2(day.rr);
                    weekReport.vol += roundTo2(day.vol);
                    weekReport.budget += roundTo2(day.budget);
                }
                // Consolidate all project stats
                // Find all jobs worked on during report period
                const timeCards = await this.crewService.getTimeCardsForQuery({
                    deleted: { $ne: true },
                    timeIn: { $gte: reportStart, $lte: reportEnd },
                    memberId: { $in: memberIdArray },
                });

                const allJobsWorkedOn = timeCards.map((card) => card.projectId);
                const uniqueJobs = dedupeArray(allJobsWorkedOn);
                for (const projectId of uniqueJobs) {
                    const jobObj = {
                        oppId: projectId,
                        hours: 0,
                        pwExtras: 0,
                        pwHourly: 0,
                        pwSqs: 0,
                        pwTotal: 0,
                        travel: 0,
                        rr: 0,
                        vol: 0,
                        budget: 0,
                    };
                    for (const day of dayReports) {
                        for (const project of day.projects) {
                            if (projectId === project.oppId) {
                                jobObj.hours += project.hours;
                                jobObj.pwExtras += project.pwExtras;
                                jobObj.pwHourly += project.pwHourly;
                                jobObj.pwSqs += project.pwSqs;
                                jobObj.pwTotal += roundTo2(project.pwTotal);
                                jobObj.travel += project.travel;
                                jobObj.rr += roundTo2(project.rr);
                                jobObj.vol += roundTo2(project.vol);
                                jobObj.budget += roundTo2(project.budget);
                            }
                        }
                    }
                    weekReport.projects.push(jobObj);
                }
                // Consolidate all member stats
                for (const mem of weekActiveMembers) {
                    const memObj: any = {
                        memberId: mem.memberId,
                        name: mem.memberName,
                        hours: 0,
                        pwExtras: 0,
                        pwHourly: 0,
                        pwSqs: 0,
                        pwTotal: 0,
                        leadBonus: 0,
                        travel: 0,
                        rr: 0,
                        vol: 0,
                        budget: 0,
                        daysOff: 0,
                        ptoUsed: 0,
                        baseWage: 0,
                        totalEarned: 0,
                        cards: [],
                    };
                    for (const day of dayReports) {
                        for (const member of day.members) {
                            if (memObj.memberId === member.memberId) {
                                if (member.dayOff) memObj.daysOff++;
                                if (member.ptoUsed) memObj.ptoUsed++;
                                memObj.wageType = member.wageType;
                                memObj.wage = member.wage;
                                memObj.hours += member.hours;
                                memObj.pwExtras += member.pwExtras;
                                memObj.pwHourly += member.pwHourly;
                                memObj.pwSqs += member.pwSqs;
                                memObj.pwTotal += member.pwTotal;
                                // if (!member.lead) memObj.pwTotal += member.pwTotal;
                                if (member.lead) memObj.leadBonus += member.leadBonus;
                                memObj.travel += member.travel;
                                memObj.rr += roundTo2(member.rr);
                                memObj.vol += roundTo2(member.vol);
                                memObj.budget += roundTo2(member.budget);
                                memObj.baseWage += member.dayBase;
                                memObj.totalEarned += member.totalEarned;
                                memObj.cards.push(...member.cards);
                            }
                        }
                    }
                    memObj.percent = roundTo3(memObj.rr / weekReport.rr);
                    weekReport.members.push(memObj);
                }
                // Calculate weekly Pay
                weekReport.actual = 0;
                for (const member of weekReport.members) {
                    if (member.wageType === "hourly" && member.hours > 40) {
                        member.otHours = member.hours - 40;
                        member.otPay = member.otHours * member.wage * 0.5;
                        member.baseWage += member.otPay;
                        member.totalEarned += member.otPay;
                    }
                    member.actual = member.totalEarned;
                    member.rrDollar = roundTo2(member.rr / member.actual);
                    weekReport.actual += member.actual;
                }
                weekReport.rrDollar = roundTo2(weekReport.rr / weekReport.actual);

                weekTotals.budget += roundTo2(weekReport.budget);
                weekTotals.rr += roundTo2(weekReport.rr);
                weekTotals.vol += roundTo2(weekReport.vol);
                weekTotals.actual += roundTo2(weekReport.actual);
                weekTotals.crews.push(weekReport);
            }

            // Calculate Subcontractor stats
            const subArr = subs.map((s) => s._id);

            const allSubLogs = await this.getDailylogFroQuery({
                crewId: { $in: subArr },
                deleted: { $ne: true },
                date: { $gte: reportStart, $lte: reportEnd },
            });
            for (const sub of subs) {
                const subLogs = allSubLogs.filter((s) => s.crewId === sub._id);

                // if (!subLogs.length) return false;

                const subReport: any = {
                    crewId: sub._id,
                    name: sub.name,
                    type: "sub",
                    budget: 0,
                    actual: 0,
                    rr: 0,
                    vol: 0,
                    projects: [],
                    dayReports: [],
                };

                for (const log of subLogs) {
                    const dayReport = await this.subcontractorService.subDayActivity(sub, log, companyId);
                    const arr = dayReport.projects.map((a) => a.oppPO).filter((str) => str !== "");

                    dayReport.POList = arr;
                    dayReport.date = log.date;

                    dayReport.log = log;
                    // Include burden in sub's budget
                    dayReport.budget += dayReport.laborBurden;
                    subReport.dayReports.push(dayReport);
                    subReport.budget += dayReport.budget;
                    subReport.actual += dayReport.actual;
                    subReport.rr += dayReport.rr;
                    subReport.vol += dayReport.vol;
                    subReport.projects.push(...dayReport.projects);
                }

                subReport.rrDollar = subReport.rr / subReport.actual;

                weekTotals.budget += roundTo2(subReport.budget);
                weekTotals.rr += roundTo2(subReport.rr);
                weekTotals.vol += roundTo2(subReport.vol);
                weekTotals.actual += roundTo2(subReport.actual);
                weekTotals.crews.push(subReport);
            }
            weekTotals.rrDollar = roundTo2(weekTotals.rr / weekTotals.actual);
            return weekTotals;
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async getDailylogFroQuery(query) {
        try {
            const dailylog = await this.dailyLogModel
                .aggregate([
                    {
                        $match: query,
                    },
                    { $unwind: "$projects" }, // Unwind projects array
                    {
                        $lookup: {
                            from: "Opportunity",
                            localField: "projects.oppId",
                            foreignField: "_id",
                            as: "opp",
                            pipeline: [{ $project: { PO: 1, num: 1 } }],
                        },
                    },
                    {
                        $unwind: {
                            path: "$opp",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $addFields: {
                            "projects.oppPO": {
                                $cond: {
                                    if: { $eq: [{ $type: "$opp" }, "object"] },
                                    then: { $concat: ["$opp.PO", "-", "$opp.num"] },
                                    else: "$projects.oppPO",
                                },
                            },
                        },
                    },
                    { $unset: "opp" },
                    {
                        $group: {
                            _id: "$_id",
                            projects: { $push: "$projects" },
                            otherFields: { $first: "$$ROOT" }, // Preserve all other fields
                        },
                    },
                    {
                        $replaceRoot: {
                            newRoot: {
                                $mergeObjects: ["$otherFields", { projects: "$projects" }], // Merge all fields including projects
                            },
                        },
                    },
                ])
                .exec();

            return dailylog;
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // Returns the stats for a crew for one day
    // Needs: Crews, Timesheets, DailyLogs,
    async crewDayActivity(
        startOfDay: Date,
        crewId: string,
        companyId: string,
        pwSettingAllData,
        allWorkTaskData,
        companySetting,
    ) {
        try {
            const end = new Date(startOfDay);
            end.setDate(end.getDate() + 1);
            end.setMilliseconds(end.getMilliseconds() - 1);

            const crew = (
                await this.crewModel.aggregate([
                    {
                        $match: {
                            _id: crewId,
                            companyId,
                            deleted: { $ne: true },
                        },
                    },
                    {
                        $lookup: {
                            from: "CrewMember",
                            localField: "_id",
                            foreignField: "crewId",
                            as: "members",
                        },
                    },
                    {
                        $limit: 1, // Limit the result to only one crew
                    },
                ])
            )[0];

            const activeMembers = crew.members
                .filter((member) => {
                    return activeCrewMember(member, startOfDay, end);
                })
                .map((member) => member);
            const memberIdArray = activeMembers.map((mem) => mem.memberId);

            const timeCards = await this.crewService.getTimeCardsForQuery({
                memberId: { $in: memberIdArray },
                timeIn: { $gte: startOfDay, $lte: end },
                deleted: { $ne: true },
            });

            const log = (
                await this.getDailylogFroQuery({
                    crewId: crewId,
                    deleted: { $ne: true },
                    date: { $gte: startOfDay, $lte: end },
                })
            )[0];
            // Define crew report for day
            const dayReport = {
                hours: 0,
                baseWage: 0,
                pwExtras: 0,
                pwHourly: 0,
                pwSqs: 0,
                pwTotal: 0,
                travel: 0,
                rr: 0,
                vol: 0,
                budget: 0,
                actual: 0,
                tearOffSq: 0,
                roofingSq: 0,
                members: [],
                projects: [],
                log,
                mError: [],
                LogPOList: [],
            };

            // Calculate Crew Lead Bonus
            const crewLeadId = findCrewLeadId(crew, startOfDay);

            // Calculate what happened on the day for each member
            const POListSet = new Set();
            const memberDayReports = await Promise.all(
                activeMembers.map(async (member) => {
                    const memObj = await this.crewService.memberDayActivity(
                        companyId,
                        member.memberId,
                        startOfDay,
                        end,
                        crewLeadId,
                        pwSettingAllData,
                        allWorkTaskData,
                        companySetting,
                    );
                    memObj.POList.forEach((p) => {
                        if (p !== "companyDefaultPO" && p !== "") {
                            POListSet.add(p);
                        }
                    });
                    return memObj;
                }),
            );

            // for dailylog dropdown PO list
            const poData = await this.crewService.getPOList(POListSet, companyId);
            dayReport.LogPOList = poData;

            memberDayReports.map((member) => {
                if (member.memberId === crewLeadId) member.crewLead = true;
            });
            const leadBonus = calcCrewLeadBonus(memberDayReports);
            // Add up actuals and apply crew lead bonus
            memberDayReports.map((member) => {
                if (member.crewLead) {
                    member.lead = true;
                    member.leadBonus = member.dayOff ? 0 : member?.cards?.length === 0 ? 0 : leadBonus;
                    member.totalEarned += member.leadBonus;
                }
                dayReport.baseWage += member.dayBase;
                dayReport.actual += member.totalEarned;
            });

            // Calculate project stats
            // Pull projects from time cards
            const cardProjectArray = timeCards.map((card) => card.projectId);
            const uniqueCardProjectIds = dedupeArray(cardProjectArray);

            // Remove company PO & Days Off (don't need logs for this PO)
            const poIndex = uniqueCardProjectIds.findIndex((id) => id === "companyDefaultPO");

            if (poIndex > -1) uniqueCardProjectIds.splice(poIndex, 1);
            const dayOffIndex = uniqueCardProjectIds.findIndex((id) => id === "");

            if (dayOffIndex > -1) uniqueCardProjectIds.splice(dayOffIndex, 1);
            // Pull projects from daily log
            const logProjectArray = log?.projects?.map((project) => project.oppId);

            const uniqueLogProjectIds = dedupeArray(logProjectArray);
            // Compare to see if the same
            const sameProjects = arraysAreEqual(uniqueCardProjectIds, uniqueLogProjectIds);

            // Error handling for time card & dailylog PO's
            if (!sameProjects.equal) {
                if (sameProjects?.nonMatchingValues.length) {
                    const missingProjects = timeCards
                        .filter((card) => sameProjects.nonMatchingValues.includes(card.projectId))
                        .map((card) => card.projectPO)
                        .filter((po) => po !== null);

                    const source = missingProjects.length > 1 ? "Daily Logs" : "Daily Log";
                    dayReport.mError.push(
                        `Missing ${source} for PO: ${[...new Set(missingProjects)].join(
                            ", ",
                        )} on ${shortenDate(startOfDay)} for ${crew.name}!`,
                    );
                }

                if (sameProjects?.nonMatchingLog.length) {
                    const missingLog = log?.projects
                        .filter((dLog) => sameProjects.nonMatchingLog.includes(dLog.oppId))
                        .map((dLog) => dLog.oppPO)
                        .filter((po) => po !== null);

                    const source2 = missingLog.length > 1 ? "Time Cards" : "Time Card";
                    dayReport.mError.push(
                        `Daily Log PO has no ${source2} on ${shortenDate(startOfDay)} for ${
                            crew.name
                        }: ${missingLog.join(", ")}`,
                    );
                }
            }

            await Promise.all(
                uniqueCardProjectIds.map(async (projectId) => {
                    let projectStats = {
                        oppId: projectId,
                        oppPO: "",
                        rr: 0,
                        vol: 0,
                        budget: 0,
                        tearOffDone: 0,
                        roofingDone: 0,
                        hours: 0,
                        pwExtras: 0,
                        pwHourly: 0,
                        pwSqs: 0,
                        pwTotal: 0,
                        travel: 0,
                    };
                    // Do nothing on NHR and Day Off
                    if (projectId && projectId !== "companyDefaultPO") {
                        const projectLog = log?.projects?.find((proj) => proj.oppId === projectId);
                        const stats = await this.crewService.projectDayCompleted(projectLog, companyId);

                        projectStats = { ...projectStats, ...stats };
                    }
                    dayReport.rr += projectStats.rr;
                    dayReport.vol += projectStats.vol;
                    dayReport.budget += projectStats.budget;
                    dayReport.tearOffSq += projectStats.tearOffDone;
                    dayReport.roofingSq += projectStats.roofingDone;
                    // Calc member totals on project for the day
                    memberDayReports.map((member) => {
                        member?.projects?.map((project) => {
                            if (project.oppId === projectId) {
                                projectStats.hours += project.hours;
                                projectStats.pwExtras += project.pwExtras;
                                projectStats.pwHourly += project.pwHourly;
                                projectStats.pwSqs += project.pwSqs;
                                projectStats.pwTotal += project.pwTotal;
                                projectStats.travel += project.travel;
                                dayReport.hours += project.hours;
                                dayReport.pwExtras += project.pwExtras;
                                dayReport.pwHourly += project.pwHourly;
                                dayReport.pwSqs += project.pwSqs;
                                dayReport.pwTotal += project.pwTotal;
                                dayReport.travel += project.travel;
                            }
                        });
                    });
                    // Add percent of RR & Vol to each member's report
                    memberDayReports.map((member) => {
                        member?.projects?.map((project) => {
                            if (project.oppId === projectId) {
                                project.percent = roundTo3(project.pwTotal / projectStats.pwTotal);
                                project.budget = roundTo2(project.percent * projectStats.budget);
                                project.rr = roundTo2(project.percent * projectStats.rr);
                                project.vol = roundTo2(project.percent * projectStats.vol);
                            }
                        });
                    });

                    dayReport.projects.push(projectStats);
                }),
            );
            // Add up member totals
            memberDayReports.map((member) => {
                member.budget = 0;
                member.rr = 0;
                member.vol = 0;
                member?.projects?.map((project) => {
                    member.budget += project.budget || 0;
                    member.rr += project.rr || 0;
                    member.vol += project.vol || 0;
                });
                member.percent = roundTo3(member.rr / dayReport.rr);
            });
            dayReport.members = memberDayReports;

            return dayReport;
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async kpiReport(userId: string, companyId: string, reportStart: Date, reportEnd: Date) {
        try {
            const dateStart = new Date(reportStart);
            const dateEnd = new Date(reportEnd);
            const report: any = {};

            const [leadTypes, opps, timeCards, checkpoint] = await Promise.all([
                this.projectTypeModel.find({ companyId, deleted: false }),
                this.opportunityModel.aggregate([
                    {
                        $match: {
                            companyId,
                            deleted: { $ne: true },
                            // status: { $ne: "inactive" },
                        },
                    },
                    {
                        $set: {
                            oppType: {
                                $cond: {
                                    if: {
                                        $and: [
                                            { $gt: ["$saleDate", null] },
                                            { $gt: ["$acceptedType", null] },
                                        ],
                                    },
                                    then: "$acceptedType",
                                    else: "$oppType",
                                },
                            },
                        },
                    },
                    {
                        $lookup: {
                            from: "ProjectType",
                            foreignField: "_id",
                            localField: "acceptedType",
                            as: "projectType",
                            pipeline: [{ $project: { _id: 1, name: 1, typeReplacement: 1 } }],
                        },
                    },
                    {
                        $unwind: {
                            path: "$projectType",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Project",
                            foreignField: "_id",
                            localField: "acceptedProjectId",
                            as: "projectData",
                            pipeline: [{ $project: { _id: 1, "customData.reroofAreas": 1 } }],
                        },
                    },
                    {
                        $unwind: {
                            path: "$projectData",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Contact",
                            pipeline: [
                                {
                                    $project: {
                                        _id: 1,
                                        fullName: 1,
                                        businessName: 1,
                                        isBusiness: 1,
                                    },
                                },
                            ],
                            foreignField: "_id",
                            localField: "contactId",
                            as: "contactId",
                        },
                    },
                    {
                        $unwind: {
                            path: "$contactId",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "LeadSource",
                            localField: "leadSourceId",
                            foreignField: "_id",
                            pipeline: [{ $project: { name: 1 } }],
                            as: "leadSourceObj",
                        },
                    },
                    {
                        $lookup: {
                            from: "Member",
                            localField: "salesPerson",
                            foreignField: "_id",
                            as: "assignedTo",
                            pipeline: [{ $project: { name: 1 } }],
                        },
                    },
                    {
                        $addFields: {
                            fullName: "$contactId.fullName",
                            lastName: "$contactId.lastName",
                            leadSource: { $arrayElemAt: ["$leadSourceObj.name", 0] },
                            assignedTo: { $arrayElemAt: ["$assignedTo.name", 0] },
                        },
                    },
                    {
                        $project: {
                            order: 0,
                            comments: 0,
                            activities: 0,
                            stepsChecklist: 0,
                            leadSourceObj: 0,
                            companyId: 0,
                            companyLang: 0,
                            companyLat: 0,
                            dateReceived: 0,
                            distance: 0,
                            duration: 0,
                            editedBy: 0,
                            currDate: 0,
                            createdBy: 0,
                            createdAt: 0,
                            updatedAt: 0,
                            actions: 0,
                            nextAction: 0,
                            taxJurisdiction: 0,
                            zip: 0,
                            __v: 0,
                            deleted: 0,
                        },
                    },
                    // { $sort: { createdAt: -1 } },
                ]),
                this.crewService.getTimeCardsForQuery({
                    companyId,
                    timeIn: {
                        $gte: dateStart,
                        $lte: dateEnd,
                    },
                    active: { $ne: true },
                    deleted: { $ne: true },
                }),
                this.checkpointModel.find({
                    companyId,
                    stageGroup: { $in: [StageGroupEnum.Sales, StageGroupEnum.Operations] },
                    deleted: { $ne: true },
                }),
            ]);

            if (!opps.length) return report;

            const dynamicVars = {};

            report.checkpoints = {};

            //Loop for checkpoints data
            await Promise.all(
                checkpoint.map(async (data) => {
                    const name = data.name; // .replace(/\s+/g, "");
                    //these are always defalut
                    if (data.symbol === "oppDate") {
                        dynamicVars[name] = this.getReportCount(opps, data.symbol, dateStart, dateEnd);

                        report.checkpoints[name] = {};
                        report.checkpoints[name].symbol = data.symbol;
                        report.checkpoints[name].sequence = data.sequence;
                        report.checkpoints[name].num = dynamicVars[name].num;
                        report.checkpoints[name].opps = dynamicVars[name].opps
                            .sort(dynamicSort("lastName"))
                            .sort(dynamicSort(data.symbol));

                        //Go through all leads
                        const allLeadSourceIds = [];
                        for (const opp of report.checkpoints[name].opps) {
                            allLeadSourceIds.push(opp.leadSource);
                        }
                        // Get list of unique sources
                        const uniqueSources = allLeadSourceIds.filter((item, index) => {
                            return allLeadSourceIds.indexOf(item) >= index;
                        });
                        report.checkpoints[name].leadSources = [];
                        for (const source of uniqueSources) {
                            const obj: any = {};
                            obj.id = source;
                            obj.num = 0;
                            obj.cost = 0;
                            obj.opps = 0;
                            obj.sales = 0;
                            obj.volume = 0;
                            obj.realRev = 0;
                            obj.oppData = [];
                            for (const opp of report.checkpoints[name].opps) {
                                if (source === opp.leadSource) {
                                    if (!obj.name) obj.name = opp.leadSource;
                                    obj.num++;
                                    if (opp.leadCost) obj.cost += opp.leadCost;
                                    if (!opp.saleDate) {
                                        opp.soldValue = 0;
                                        opp.realRevValue = 0;
                                    }
                                    if (opp.soldValue) obj.volume += opp.soldValue;
                                    if (opp.realRevValue) obj.realRev += opp.realRevValue;
                                    if (opp.oppDate) obj.opps++;
                                    if (opp.saleDate) obj.sales++;

                                    obj.oppData.push(opp);
                                }
                            }
                            obj.costPerOpp = obj.opps ? Math.round(obj.cost / obj.opps) : "no opps";
                            obj.costPerSale = obj.sales ? Math.round(obj.cost / obj.sales) : "no sales";
                            obj.rrPerLead = Math.round(obj.realRev / obj.num);
                            obj.cost = Math.round(obj.cost);
                            obj.volume = Math.round(obj.volume);
                            obj.realRev = Math.round(obj.realRev);
                            report.checkpoints[name].leadSources.push(obj);
                        }
                        report.checkpoints[name].leadSources.sort(dynamicSort("-rrPerLead"));
                    } else if (data.symbol === "saleDate") {
                        dynamicVars[name] = await this.getReportCountByProjectType(
                            opps.sort(dynamicSort("lastName")).sort(dynamicSort(data.symbol)),
                            data.symbol,
                            dateStart,
                            dateEnd,
                            leadTypes,
                        );
                        report.checkpoints[name] = {};
                        report.checkpoints[name].symbol = data.symbol;
                        report.checkpoints[name].sequence = data.sequence;
                        report.checkpoints[name].num = dynamicVars[name].totalNum; // report.sales.repairRoof.num + report.sales.newRoof.num;
                        report.checkpoints[name].vol = roundTo2(dynamicVars[name].vol);
                        report.checkpoints[name].rr = roundTo2(dynamicVars[name].rr);
                        report.checkpoints[name].types = dynamicVars[name].types;
                    } else if (data.symbol === "jobCompletedDate") {
                        const completed = await this.getReportCountByProjectType(
                            opps.sort(dynamicSort("lastName")).sort(dynamicSort(data.symbol)),
                            data.symbol,
                            dateStart,
                            dateEnd,
                            leadTypes,
                        );

                        report.completed = {};
                        report.completed.symbol = data.symbol;
                        report.completed.sequence = data.sequence;
                        report.completed.num = completed.totalNum; // report.completed.repairRoof.num + report.completed.newRoof.num;
                        report.completed.vol = roundTo2(completed.vol);
                        report.completed.rr = roundTo2(completed.rr);
                        report.completed.types = completed.types;
                    } else if (data.symbol === "jobStartedDate") {
                        const started = this.getReportCount(opps, data.symbol, dateStart, dateEnd);
                        report.started = {};
                        report.started.symbol = data.symbol;
                        report.started.sequence = data.sequence;
                        report.started = started.num;
                        report.startedOpps = started.opps;
                        report.startedVol = 0;
                        report.startedRR = 0;
                    }
                    //for rest of checkpoints
                    else {
                        // const name = data.name.replace(/\s+/g, "");
                        dynamicVars[name] = this.getReportCount(opps, data.symbol, dateStart, dateEnd);

                        report.checkpoints[name] = {};
                        report.checkpoints[name].symbol = data.symbol;
                        report.checkpoints[name].sequence = data.sequence;
                        report.checkpoints[name].num = dynamicVars[name].num;
                        report.checkpoints[name].opps = dynamicVars[name].opps
                            .sort(dynamicSort("lastName"))
                            .sort(dynamicSort(data.symbol));
                    }
                }),
            );
            const upcoming = this.getReportBetweenKpi(opps, "saleDate", "jobStartedDate", dateEnd);

            report.upcoming = upcoming.num;
            report.upcomingOpps = upcoming.opps;
            report.upcomingVol = 0;
            report.upcomingRR = 0;
            report.manHrs = 0;
            report.laborPaid = 0;

            const lost = this.getReportCount(opps, "lostDate", dateStart, dateEnd);
            report.lost = lost.num;
            report.lostOpps = lost.opps;

            //Add up all value and RR of jobs started during the week
            for (let i = 0; i < report.startedOpps.length; i++) {
                report.startedVol += report.startedOpps[i].soldValue;
                report.startedRR += report.startedOpps[i].realRevValue;
            }
            //Add up all value and RR of upcoming jobs and
            for (let i = 0; i < report.upcomingOpps.length; i++) {
                report.upcomingVol += report.upcomingOpps[i].soldValue;
                report.upcomingRR += report.upcomingOpps[i].realRevValue;
            }
            for (let i = 0; i < timeCards.length; i++) {
                if (timeCards[i].projectId !== "companyDefaultPO") {
                    report.manHrs += timeCards[i].hrs;
                    report.laborPaid += timeCards[i].hrs * 18;
                }
            }

            return report;
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // Collect all opportunites where a prop matches a start and end date
    getReportCount(opps, prop, dateStart, dateEnd) {
        const report: any = {};
        report.num = 0;
        report.opps = [];
        for (let i = 0; i < opps.length; i++) {
            if (new Date(opps[i][prop]) >= dateStart && new Date(opps[i][prop]) <= dateEnd) {
                report.num++;
                report.opps.push(opps[i]);
            }
        }
        return report;
    }

    // Collect all opportunites where a prop is between a start and end date
    async getReportCountByProjectType(opps, prop, dateStart, dateEnd, leadTypes) {
        const data = {
            totalNum: 0,
            vol: 0,
            rr: 0,
            types: [],
        };
        for (const type of leadTypes) {
            const report: any = {};
            report.id = type._id;
            report.name = type.name;
            report.typeReplacement = type.typeReplacement;
            report.num = 0;
            report.opps = [];
            report.vol = 0;
            report.rr = 0;

            opps.map((opp) => {
                if (new Date(opp[prop]) >= dateStart && new Date(opp[prop]) <= dateEnd && opp.orderId) {
                    const projectTypes = opp.projectType;

                    if (projectTypes._id === type._id) {
                        report.num++;
                        report.opps.push(opp);

                        //Add up all value and real revenue for each job type
                        report.vol += opp.soldValue;
                        report.rr += opp.realRevValue;

                        //Only for new roof & Completed projects
                        if (projectTypes.typeReplacement && prop === "jobCompletedDate") {
                            const pitchMod = {};
                            const pitchModData = type?.pitchMod;
                            for (const key in pitchModData) {
                                if (Object.hasOwnProperty.call(pitchModData, key)) {
                                    const modifiedKey = key.split("/")[0];
                                    pitchMod[modifiedKey] = pitchModData[key];
                                }
                            }

                            const project: any = opp.projectData;
                            opp.squares = 0;

                            project?.customData?.reroofAreas.map((area) => {
                                let mod = 0;
                                const pitchModKeys = Object.keys(pitchMod);
                                for (let i = 0; i < Object.keys(pitchMod).length; i++) {
                                    if (area.pitch >= i && area.pitch < i + 1) {
                                        mod = 1 + pitchMod[pitchModKeys[i]];
                                    }
                                }
                                const layerMod = 1 + (area.layers - 1) * 0.5;

                                const squares = area.install + area.remove * layerMod;
                                opp.squares += squares * mod;
                            });

                            opp.RRSQ = roundTo2(opp.realRevValue / opp.squares);
                            opp.volSQ = roundTo2(opp.soldValue / opp.squares);

                            if (opp?.ttlExtraWork) {
                                report.vol += opp.ttlExtraWork;
                            }
                            //add $52 RR for each sheet of plywood
                            if (opp?.extraPly) {
                                //TODO: extraPly does not exist on opp
                                report.rr += opp.extraPly * 52;
                            }
                            if (opp?.manHoursCost) {
                                //TODO: manHoursCost does not exist on opp
                                report.rr += opp.manHoursCost;
                            }
                        }
                    }
                }
            });
            data.totalNum += report.num;
            data.vol += report.vol;
            data.rr += report.rr;

            data.types.push(report);
        }
        return data;
    }

    // Collect all opportunites where 1 property is before a date and
    // the other is after or doesn't exist yet
    getReportBetweenKpi(opps, prop, prop2, dateEnd) {
        try {
            const filteredOpps = opps.filter((opp) => {
                const propValue = opp[prop];
                const prop2Value = opp[prop2];

                return (
                    propValue &&
                    new Date(propValue) <= dateEnd &&
                    // propValue instanceof Date &&
                    opp.orderId &&
                    (!prop2Value || new Date(prop2Value) > dateEnd)
                );
            });

            const report = {
                num: filteredOpps.length,
                opps: filteredOpps,
            };

            return report;
        } catch (error: any) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async productionReport(userId: string, companyId: string, startDate: Date, endDate: Date, user: any) {
        try {
            const reportStart = new Date(startDate);
            const reportEnd = new Date(endDate);

            const { members } = await this.positionService.getManagedMembersInternal(
                user.memberId,
                companyId,
                user.teamPermission,
                user.symbol,
            );

            const [projectTypes, coVar, allOpps] = await Promise.all([
                this.projectTypeModel.find({
                    companyId,
                    deleted: false,
                }),
                this.companySettingModel.findOne({ companyId, deleted: false }),
                this.opportunityModel.aggregate([
                    {
                        $match: {
                            companyId,
                            deleted: false,
                            jobCompletedDate: { $gte: reportStart, $lte: reportEnd },
                            $or: [
                                { salesPerson: { $in: members } },
                                { projectManager: { $in: members } },
                                { "workingCrew.id": { $in: members } },
                            ],
                        },
                    },
                    {
                        $set: {
                            oppType: {
                                $cond: {
                                    if: {
                                        $and: [
                                            { $gt: ["$saleDate", null] },
                                            { $gt: ["$acceptedType", null] },
                                        ],
                                    },
                                    then: "$acceptedType",
                                    else: "$oppType",
                                },
                            },
                        },
                    },
                    {
                        $lookup: {
                            from: "Order",
                            foreignField: "_id",
                            localField: "orderId",
                            as: "order",
                        },
                    },
                    {
                        $unwind: {
                            path: "$order",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Price",
                            foreignField: "_id",
                            localField: "order.projectPriceId",
                            as: "projectPrice",
                        },
                    },
                    {
                        $unwind: {
                            path: "$projectPrice",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Contact",
                            foreignField: "_id",
                            localField: "contactId",
                            as: "contact",
                        },
                    },
                    {
                        $unwind: {
                            path: "$contact",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Member",
                            foreignField: "_id",
                            localField: "projectManager",
                            as: "projectManagerDetails",
                        },
                    },
                    {
                        $unwind: {
                            path: "$projectManagerDetails",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Member",
                            foreignField: "_id",
                            localField: "salesPerson",
                            as: "salesPersonDetails",
                        },
                    },
                    {
                        $unwind: {
                            path: "$salesPersonDetails",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "ProjectType",
                            foreignField: "_id",
                            localField: "acceptedType",
                            as: "acceptedProjectDetails",
                        },
                    },
                    {
                        $unwind: {
                            path: "$acceptedProjectDetails",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $unwind: {
                            path: "$order.projects",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Package",
                            localField: "order.projects.basePackage",
                            foreignField: "_id",
                            as: "order.projects.packageDetails",
                        },
                    },
                    {
                        $unwind: {
                            path: "$order.projects.packageDetails",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Options",
                            localField: "order.projects.chosenOptions",
                            foreignField: "_id",
                            as: "order.projects.options",
                        },
                    },
                    {
                        $group: {
                            _id: "$_id",
                            packages: {
                                $addToSet: {
                                    name: "$order.projects.packageDetails.name",
                                    order: "$order.projects.packageDetails.order",
                                },
                            },
                            options: {
                                $addToSet: {
                                    name: "$order.projects.options.name",
                                    order: "$order.projects.options.order",
                                },
                            },
                            data: { $first: "$$ROOT" },
                        },
                    },
                    {
                        $replaceRoot: {
                            newRoot: {
                                $mergeObjects: ["$data", { packages: "$packages", options: "$options" }],
                            },
                        },
                    },
                    {
                        $project: {
                            _id: 1,
                            companyId: 1,
                            oppType: 1,
                            contactId: 1,
                            "contact.fullName": 1,
                            "contact.businessName": 1,
                            "contact.isBusiness": 1,
                            "salesPersonDetails.name": 1,
                            PO: 1,
                            num: 1,
                            salesPerson: 1,
                            stage: 1,
                            orderId: 1,
                            jobCompletedDate: 1,
                            acceptedProjectId: 1,
                            acceptedType: 1,
                            "order.priceTotals": 1,
                            "order.actualTotals": 1,
                            "order.modifiedBudget": 1,
                            projectPrice: 1,
                            changeOrders: 1,
                            salesCommission: 1,
                            workingCrew: 1,
                            "projectManagerDetails.name": 1,
                            soldValue: 1,
                            ttlExtraWork: 1,
                            changeOrderValue: 1,
                            "acceptedProjectDetails.name": 1,
                            warrantyType: 1,
                            financeFee: 1,
                            budgetScore: 1,
                            packages: 1,
                            options: 1,
                        },
                    },
                ]),
            ]);

            const completedOpps = [];
            let totalVolumeOfProject = 0;
            for (let i = 0; i < allOpps.length; i++) {
                const opp: any = allOpps[i];
                const order: any = opp?.order;

                const oppId = opp._id;
                // @TODO : projectPrice will always be available
                if (!order || !opp.acceptedProjectId || !opp?.projectPrice) continue;
                const oppComm = await this.commissionModificationModel.find({ oppId, companyId });
                const crewReport = await this.crewProjectReport(companyId, oppId);
                const { hrs, allWorkOrders } = crewReport;

                const logs = await this.getDailylogFroQuery({
                    projects: {
                        $elemMatch: {
                            oppId,
                        },
                    },
                    deleted: { $ne: true },
                });

                const price = order?.priceTotals;
                const projectPrice = opp?.projectPrice;
                const hourCost = projectPrice?.variables?.manHourRate;
                const plyCost = projectPrice?.variables?.plywoodRate;
                const matMarkup = projectPrice?.variables?.matMarkup * 100;

                const {
                    data: { extras },
                } = await this.dailyLogService.dailylogExtraWorkForOpp(companyId, opp._id);

                const logTotals = {
                    roofSq: 0,
                    tearSq: 0,
                    plywood: 0,
                    hours: 0,
                    matCosts: 0,
                };

                logs.map((log) => {
                    const opp = log.projects.find((project) => {
                        return project.oppId === oppId;
                    });

                    logTotals.roofSq += roundTo2(opp?.roofingDone || opp?.roofingSQ || 0);
                    logTotals.tearSq += roundTo2(opp.tearOffDone || opp?.tearOffSQ || 0);
                    logTotals.plywood += roundTo2(opp.plywoodReplaced || opp?.instSheet || 0);
                    logTotals.hours += roundTo2(opp.manHours || opp?.addManHours || 0);
                    logTotals.matCosts += roundTo2(opp.materialCosts || opp?.addMaterials || 0);

                    const crewId = log.crewId;
                    const date = new Date(log.date);
                    const highTemp = log.highTemp;
                    const lowTemp = log.lowTemp;
                    const maxwind_mph = log.maxwind_mph;
                    const totalprecip_in = log.totalprecip_in;
                    // const weather = log.weather;
                    const worked = log.worked;
                    const numProjects = log.projects.length;
                    return {
                        id: log._id,
                        opp,
                        crewId,
                        date,
                        highTemp,
                        lowTemp,
                        maxwind_mph,
                        totalprecip_in,
                        worked,
                        numProjects,
                    };
                });

                // function to calculate volume and other calc
                const { laborBudget, materialBudget, newTotal, totalManHours, workTaskCost } =
                    this.calculateProjectVolume(
                        opp?.changeOrderValue,
                        opp.soldValue,
                        allWorkOrders,
                        price,
                        projectPrice.variables,
                        opp?.changeOrders,
                        order?.modifiedBudget,
                        logTotals,
                    );

                const rawEstimatedHrs =
                    allWorkOrders?.reduce((sum, item) => {
                        return sum + (item?.ttlHours || 0);
                    }, 0) || 0;

                // budget
                const budget = {
                    total: newTotal,
                    mTotal: materialBudget,
                    lTotal: laborBudget,
                    commission: price?.commission || 0,
                    mTotalP: newTotal ? Math.round((materialBudget / newTotal) * 100) : 0,
                    lTotalP: newTotal ? Math.round((laborBudget / newTotal) * 100) : 0,
                    jobCost: (price?.commission || 0) + materialBudget + laborBudget,
                    left: 0,
                    leftP: 0,
                    days: 0,
                    ttlHours: 0,
                };
                budget.left = roundTo2(newTotal - budget.jobCost);
                budget.leftP = newTotal ? Math.round((budget.left / newTotal) * 100) : 0;
                budget.days = roundTo1(price.overhead / coVar.dailyOH);
                budget.ttlHours =
                    allWorkOrders?.reduce((hrs, workOrder) => {
                        return hrs + (workOrder.ttlHours || 0);
                    }, 0) || 0;
                // adding tavel time to estimate hrs
                budget.ttlHours += price?.travelFee / price?.travelHrlyRate || 0;
                // adding extra time for change order labor into estimate hrs (lSubtotal/ total hrs + travel hrs)
                budget.ttlHours += extras.hours + extras.ply * 0.5;
                budget.ttlHours += totalManHours;
                if (workTaskCost) {
                    budget.ttlHours += workTaskCost / (price.lSubtotal / rawEstimatedHrs);
                }

                let hours = opp.extraWork?.hours || 0;
                let ply = opp.extraWork?.ply || 0;
                let mats = opp.extraWork?.mats || 0;

                hours += logTotals?.hours || 0;
                ply += logTotals?.plywood || 0;
                mats += logTotals.matCosts || 0;
                const totalHoursCost = roundTo2(hours * hourCost);
                const totalPlyCost = roundTo2(ply * 32 * plyCost);
                const totalMatCost = roundTo2(mats * (1 + matMarkup / 100));

                // actual
                const {
                    actualLaborCost = 0,
                    actualPrice = 0,
                    actualMatCost = 0,
                    subcontractorCost = 0,
                    actualDays = 0,
                } = order?.actualTotals || {};
                const laborBurden = actualLaborCost * coVar.ttlBurden;
                const actual: any = {
                    total: actualPrice,
                    mTotal: actualMatCost,
                    hrs,
                    lTotal: actualLaborCost + laborBurden + subcontractorCost,
                    days: actualDays,
                };
                actual.mTotalP = actual.total ? Math.round((actual.mTotal / actual.total) * 100) : 0;
                actual.lTotalP = actual.total ? Math.round((actual.lTotal / actual.total) * 100) : 0;
                actual.jobCost = roundTo2(actual.commission + actual.mTotal + actual.lTotal);
                actual.left = roundTo2(actual.total - actual.jobCost);
                actual.leftP = actual.total ? Math.round((actual.left / actual.total) * 100) : 0;
                actual.commission =
                    (order?.priceTotals?.commission || 0) +
                    (oppComm?.reduce((acc, item) => acc + (item?.amount || 0), 0) || 0);

                opp.budget = budget;
                opp.actual = actual;

                const modifiedCommission = roundTo2(
                    order?.priceTotals?.commission +
                        oppComm.reduce((sum, item) => {
                            return sum + (item?.amount || 0);
                        }, 0),
                );

                // gross calc
                const labor = actualLaborCost + laborBurden + subcontractorCost;
                const grossProfit = actualPrice - modifiedCommission - actualMatCost - labor;
                opp.grossProfit = roundTo2(grossProfit); // actual
                const cogs = materialBudget + laborBudget + (price?.commission || 0);
                opp.gross = roundTo2(newTotal - cogs); // budget
                opp.grossPercent = newTotal ? Math.round((opp.gross / newTotal) * 100) : 0;
                opp.actualGrossPercent = actual.total ? Math.round((grossProfit / actual.total) * 100) : 0;
                opp.commisionPercent = actual.total
                    ? Math.round((opp.actual?.commission / actual.total) * 100)
                    : 0;
                opp.commisionBudgetPercent = budget.total
                    ? Math.round((opp.budget?.commission / budget.total) * 100)
                    : 0;

                //opp profitScore calc
                opp.score = profitScoreCalc(
                    opp.actual.total,
                    opp.actual.mTotal,
                    opp.actual.lTotal,
                    opp.actual.commission,
                    opp?.financeFee,
                );

                const totalChangeOrder =
                    opp?.changeOrders
                        ?.filter((cOrder) => !cOrder?.signedBySales && !cOrder?.deleted) // Exclude signedBySales and deleted items
                        .reduce((total, cOrder) => total + (cOrder?.jobCost || 0), 0) || 0;

                const extraAmount = totalHoursCost + totalPlyCost + totalMatCost + totalChangeOrder;
                opp.extraAmount = extraAmount;
                totalVolumeOfProject += actual.total;

                // Add indicator boolean based on conditions
                opp.jobDone = this.jobDoneIndicator(order);

                delete opp.projectPrice;

                completedOpps.push(opp);
            }
            const result = {
                num: completedOpps.length,
                totalVolumeOfProject,
                types: [],
            };

            for (const p of projectTypes) {
                const obj = {
                    name: p.name,
                    opps: [],
                    num: 0,
                };

                // Filter completedOpps based on the project type and add to opps
                obj.opps = completedOpps.filter((opp) => opp.acceptedType === p._id && !opp.warrantyType);
                obj.num = obj.opps.length;
                result.types.push(obj);

                // Handle warranty opps
            }
            const warrantyOpps = completedOpps.filter((opp) => opp.warrantyType);

            const warrantyObj = {
                name: "Warranty",
                opps: warrantyOpps,
                num: warrantyOpps.length,
            };
            result.types.push(warrantyObj);

            return result;
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // indicator boolean based on conditions
    jobDoneIndicator(order: any) {
        return !!(
            (
                order?.actualTotals?.actualPrice && // Condition 1: Actual volume value has been entered
                (order?.actualTotals?.actualLaborCost || order?.actualTotals?.subcontractorCost) && // Condition 2: Either Labor Cost OR Subcontractor fields
                order?.actualTotals?.qbCOGS
            ) // Condition 3: COGS from QB in materials modal has been filled
        );
    }

    calculateProjectVolume(
        changeOrderValue: number,
        soldValue: number,
        allWorkOrders: any[] = [],
        price: {
            matCost: number;
            matTax: number;
            mTotal: number;
            permit: number;
            asbTest: number;
            jobTotal: number;
            lTotal: number;
            commission: number;
            overhead: number;
            lSubtotal: number;
            travelFee: number;
            travelHrlyRate: number;
        } = {
            matCost: 0,
            matTax: 0,
            mTotal: 0,
            permit: 0,
            asbTest: 0,
            jobTotal: 0,
            lTotal: 0,
            commission: 0,
            overhead: 0,
            lSubtotal: 0,
            travelFee: 0,
            travelHrlyRate: 0,
        },
        variables: {
            matMarkup: number;
            manHourRate: number;
            plywoodRate: number;
            plywoodLaborRate: number;
            ttlBurden: number;
            dailyOH: number;
        } = {
            matMarkup: 0,
            manHourRate: 0,
            plywoodRate: 0,
            plywoodLaborRate: 0,
            ttlBurden: 0,
            dailyOH: 0,
        },
        changeOrders: any[] = [],
        modifiedBudget: {
            subContractorTotal: number;
            rawLaborBudget: number;
            isSubcontractorOnly: boolean;
        },
        logTotals: {
            hours: number;
            plywood: number;
            matCosts: number;
        } = { hours: 0, plywood: 0, matCosts: 0 },
    ) {
        const {
            matCost = 0,
            matTax = 0,
            permit = 0,
            asbTest = 0,
            lSubtotal = 0,
            travelFee = 0,
            travelHrlyRate = 0,
        } = price;
        const { manHourRate = 0, plywoodRate = 0, plywoodLaborRate = 0, ttlBurden = 0 } = variables;

        const matMarkup = variables?.matMarkup * 100;

        const hours = logTotals.hours;
        const ply = logTotals.plywood;
        const mats = logTotals.matCosts;

        const totalHoursCost = roundTo2(hours * manHourRate);
        const totalPlyCost = roundTo2(ply * 32 * plywoodRate);
        const totalMatCost = roundTo2(mats * (1 + matMarkup / 100));

        const rawEstimatedHrs = allWorkOrders?.reduce((sum, item) => sum + (item?.ttlHours || 0), 0) || 0;
        const newTotal = roundTo2(
            soldValue + totalHoursCost + totalPlyCost + totalMatCost + (changeOrderValue || 0),
        );
        let chngMatMarkup = 0;
        let matChangeOrder = 0;
        let laborChangeOrder = 0;
        let laborBurdenCost = 0;
        let laborWorkTask = 0;
        let subcontractorTotal = 0;
        let totalManHours = 0;
        let workTaskCost = 0;

        changeOrders?.forEach((item) => {
            if (item && !item?.deleted) {
                chngMatMarkup += item?.mMarkup || 0;
                matChangeOrder += (item?.materials || 0) - (item?.mMarkup || 0); // to get raw mat cost (to work for old opps that dont have rawMatCost in change order)
                laborChangeOrder += (item?.labor || 0) / (1 + ttlBurden);
                laborBurdenCost += item?.laborBurden || 0;

                if (item?.isSubContractor) {
                    subcontractorTotal += item?.manHourCost || 0;
                } else {
                    laborWorkTask += item?.manHourCost || 0;
                }
                workTaskCost += item?.rawLaborCost || 0;
                totalManHours += item?.manHours || 0;
            }
        });

        // Materials include markup
        const matBreakdown = {
            cost: matCost,
            tax: matTax,
            permit,
            asbTest,
            addMaterial: totalMatCost,
            mMarkup: chngMatMarkup,
            changeOrder: matChangeOrder,
        };

        const materialBudget = roundTo2(
            matCost +
                matTax +
                permit +
                asbTest +
                chngMatMarkup +
                totalMatCost + // add'l material from daily log and change order
                matChangeOrder,
        );
        const materialBudgetPercent = newTotal ? materialBudget / newTotal : 0;

        // Labor includes travel
        /*
            Labor entered in a Change Order includes Burden so burden must be removed before adding
            Formula:
            Labor Cost: Labor amount / (1 + Burden %)
            Example: $500 Labor entered in Change Order
            Labor Cost = $500 / (1 + .265) = $395.26
            395.26 should be added to the Labor Cost
        */
        const laborBreakdown = {
            originalEstimate: lSubtotal,
            rrPlyLaborCost: plywoodLaborRate * ply,
            billableHours: roundTo2(
                hours * (lSubtotal / (rawEstimatedHrs + travelFee / travelHrlyRate) || 0), // adding travel time
            ),
            changeOrder: laborChangeOrder,
            // (changeOrders
            //     ?.filter((item) => !item?.deleted)
            //     ?.reduce((sum, item) => sum + (item?.labor || 0), 0) || 0) /
            // (1 + ttlBurden),
            // laborWorkTask,
        };

        let rawLaborBudget = roundTo2(
            lSubtotal +
                laborBreakdown.rrPlyLaborCost +
                laborBreakdown.billableHours +
                laborBreakdown.changeOrder +
                laborWorkTask,
        );

        let laborBurdenBudget = roundTo2(rawLaborBudget * ttlBurden + laborBurdenCost);
        let laborBudget = roundTo2(rawLaborBudget + laborBurdenBudget + subcontractorTotal);
        let laborBudgetPercent = newTotal ? laborBudget / newTotal : 0;

        if (modifiedBudget) {
            if (modifiedBudget.isSubcontractorOnly) {
                subcontractorTotal = modifiedBudget.subContractorTotal;
                rawLaborBudget = 0;
                laborBurdenBudget = 0;
                laborBudget = roundTo2(
                    modifiedBudget.rawLaborBudget + laborBurdenBudget + subcontractorTotal,
                );
                laborBudgetPercent = newTotal ? laborBudget / newTotal : 0;
            } else {
                subcontractorTotal = modifiedBudget.subContractorTotal;
                rawLaborBudget = modifiedBudget.rawLaborBudget;
                laborBurdenBudget = roundTo2(modifiedBudget.rawLaborBudget * ttlBurden + laborBurdenCost);
                laborBudget = roundTo2(
                    modifiedBudget.rawLaborBudget + laborBurdenBudget + subcontractorTotal,
                );
                laborBudgetPercent = newTotal ? laborBudget / newTotal : 0;
            }
        }

        return {
            newTotal,
            matBreakdown,
            materialBudget,
            materialBudgetPercent,
            rawLaborBudget,
            laborBurdenBudget,
            laborBudget,
            laborBudgetPercent,
            laborBreakdown,
            subcontractorTotal,
            totalManHours,
            workTaskCost,
        };
    }

    async projectReport(userId: string, companyId: string, oppId: string) {
        try {
            const [subs, pwSettingAllData, timeCards, oppData, crews, logs, oppComm, crewReport] =
                await Promise.all([
                    this.subcontractorModel.find({ companyId }).select("name"),

                    this.pieceWorkSettingModel.find({ companyId }),

                    this.crewService.getTimeCardsForQuery({
                        projectId: oppId, // In time card opp is project
                        deleted: { $ne: true },
                        active: { $ne: true },
                    }),
                    this.opportunityModel
                        .findOne({ _id: oppId, companyId })
                        .populate("contactId", "fullName businessName isBusiness", "Contact")
                        .populate("stage", "stageGroup", "CrmStage")
                        .select(
                            "_id PO num jobCompletedDate jobStartedDate contactId stage orderId extraWork salesCommission changeOrderValue changeOrderRRValue manHours extraPly realRevValue soldValue ttlExtraWork changeOrders",
                        ),
                    this.crewModel.aggregate([
                        {
                            $match: { companyId },
                        },
                        {
                            $lookup: {
                                from: "CrewMember",
                                localField: "_id",
                                foreignField: "crewId",
                                as: "members",
                            },
                        },
                    ]),

                    this.getDailylogFroQuery({
                        projects: {
                            $elemMatch: { oppId },
                        },
                        deleted: { $ne: true },
                    }),

                    this.commissionModificationModel.find({ oppId, companyId }),

                    this.crewProjectReport(companyId, oppId),
                ]);

            // using any type for undefined value in schema
            const opp: any = oppData;
            const order: any = await this.orderModel
                .findOne({ _id: opp?.orderId })
                .populate("projectPriceId", "variables", "Price");
            const timeIds = timeCards.map((t) => t._id);

            const pieceWork = await this.pieceWorkModel.find({
                // projectId: roofProject.oppId,
                timeCardId: { $in: timeIds },
                companyId,
                deleted: { $ne: true },
            });
            timeCards.map((card: any) => {
                const work = pieceWork.find((wrk) => wrk.timeCardId === card._id);
                card.work = work;
            });

            // piece work object
            const pwSettingMap = pwSettingAllData.reduce((map, setting) => {
                map[setting._id] = setting;
                return map;
            }, {});

            timeCards.forEach((card) => {
                card?.work?.work?.workDone.forEach((pieceWork) => {
                    const setting = pwSettingMap[pieceWork.id];
                    if (setting) {
                        // Adding name and unit to pieceWork
                        pieceWork.name = setting.name;
                        pieceWork.unit = setting.unit.split(" (")[0];
                    }
                });
            });

            let realRevBudget = 0;
            let salePrice = 0;
            let grossProfit = 0;
            const project: any = {};
            const opportunity = {
                opp: opp,
                order: {},
                commission: 0,
                modifiedCommission: 0,
                modifiedCommissionBreakDown: {
                    actual: 0,
                    modified: [],
                },
                materialBudget: 0,
                materialBudgetPercent: 0,
                matBreakdown: {
                    cost: 0,
                    tax: 0,
                    permit: 0,
                    asbTest: 0,
                    addMaterial: 0,
                    changeOrder: 0,
                    mMarkup: 0,
                },
                rawLaborBudget: 0,
                laborBurdenBudget: 0,
                laborBudget: 0,
                laborBudgetPercent: 0,
                laborBreakdown: {
                    originalEstimate: 0,
                    rrPlyLaborCost: 0,
                    billableHours: 0,
                    changeOrder: 0,
                },
                cogs: 0,
                gross: 0,
                grossPercent: 0,
                daysBudget: 0,
                rawEstimatedHrs: 0,
                manHours: 0,
                subcontractorTotal: 0,
                ttlHours: 0,
            };

            // Pull all log data
            const logTotals: any = {};

            logTotals.roofSq = 0;
            logTotals.tearSq = 0;
            logTotals.plywood = 0;
            logTotals.hours = 0;
            logTotals.matCosts = 0;

            //combined array for both crew & subcontractor
            const allCrewSub = [...crews, ...subs];

            project.logReport = logs.map((log) => {
                const opp = log.projects.find((project) => {
                    return project.oppId === oppId;
                });
                const crewData = allCrewSub.find((cw) => cw._id === log.crewId);

                logTotals.roofSq += roundTo2(opp?.roofingDone || opp?.roofingSQ || 0);
                logTotals.tearSq += roundTo2(opp.tearOffDone || opp?.tearOffSQ || 0);
                logTotals.plywood += roundTo2(opp.plywoodReplaced || opp?.instSheet || 0);
                logTotals.hours += roundTo2(opp.manHours || opp?.addManHours || 0);
                logTotals.matCosts += roundTo2(opp.materialCosts || opp?.addMaterials || 0);

                const crewId = log.crewId;
                const crewName = crewData?.name ?? "";
                const date = new Date(log.date);
                const highTemp = log.highTemp;
                const lowTemp = log.lowTemp;
                const maxwind_mph = log.maxwind_mph;
                const totalprecip_in = log.totalprecip_in;
                // const weather = log.weather;
                const worked = log.worked;
                const numProjects = log.projects.length;
                return {
                    id: log._id,
                    opp,
                    crewId,
                    crewName,
                    date,
                    highTemp,
                    lowTemp,
                    maxwind_mph,
                    totalprecip_in,
                    worked,
                    numProjects,
                };
            });
            project.logTotals = logTotals;

            const projectPrice = order?.projectPriceId;

            project.ttlBurden = projectPrice.variables.ttlBurden;
            project.plyLaborCost = projectPrice.variables?.plywoodLaborRate;
            // totalPlyCost, totalHoursCost, totalMatCost
            const hourCost = projectPrice.variables?.manHourRate;
            const plyCost = projectPrice.variables?.plywoodRate;
            const matMarkup = projectPrice.variables?.matMarkup * 100;
            // const plyLaborCost = projectPrice.variables?.plywoodLaborRate; // default value

            let hours = opp.extraWork?.hours || 0;
            let ply = opp.extraWork?.ply || 0;
            let mats = opp.extraWork?.mats || 0;

            hours += logTotals?.hours || 0;
            ply += logTotals?.plywood || 0;
            mats += logTotals.matCosts || 0;

            const totalHoursCost = roundTo2(hours * hourCost);
            const totalPlyCost = roundTo2(ply * 32 * plyCost);
            const totalMatCost = roundTo2(mats * (1 + matMarkup / 100));

            //modified commission
            project.modifiedCommission = roundTo2(
                order.priceTotals.commission +
                    oppComm.reduce((sum, item) => {
                        return sum + (item?.amount || 0);
                    }, 0),
            );

            const {
                hrs,
                cost,
                totalLeadBonus,
                pieceWorkData,
                leadIndex,
                totalTasks,
                dates,
                days,
                laborReport,
                allWorkOrders,
            } = crewReport;
            // function to calculate volume and other calc
            const {
                laborBreakdown,
                rawLaborBudget,
                laborBurdenBudget,
                laborBudget,
                laborBudgetPercent,
                matBreakdown,
                materialBudget,
                materialBudgetPercent,
                newTotal,
                subcontractorTotal,
                totalManHours,
            } = this.calculateProjectVolume(
                opp?.changeOrderValue,
                opp.soldValue,
                allWorkOrders,
                order?.priceTotals,
                projectPrice.variables,
                opp?.changeOrders,
                order?.modifiedBudget,
                logTotals,
            );
            if (order) {
                const {
                    data: { extras },
                } = await this.dailyLogService.dailylogExtraWorkForOpp(companyId, oppData?._id);
                // calculating realRevBudget value
                const perHour = 30;
                const perPly = 16;
                const extraWorkRR = (opp.manHours * perHour || 0) + (opp.extraPly * perPly || 0); //TODO: extraPly & manHours does not exist on opp
                realRevBudget = opp.realRevValue + extraWorkRR;

                // calculating salePrice value
                salePrice = opp.soldValue + (opp.ttlExtraWork || 0);

                opportunity.order = order;
                const price = order.priceTotals;

                // Commission
                opportunity.commission = price.commission;
                opportunity.modifiedCommission = roundTo2(
                    price.commission +
                        oppComm.reduce((sum, item) => {
                            return sum + (item?.amount || 0);
                        }, 0),
                );
                opportunity.modifiedCommissionBreakDown = {
                    actual: price.commission,
                    modified: oppComm.map((item) => {
                        return { amount: item?.amount || 0 };
                    }, 0),
                };

                // Materials include markup
                opportunity.matBreakdown = matBreakdown;
                opportunity.materialBudget = materialBudget;
                opportunity.materialBudgetPercent = materialBudgetPercent;

                // Labor includes travel
                opportunity.laborBreakdown = laborBreakdown;
                opportunity.manHours = totalManHours;
                opportunity.subcontractorTotal = subcontractorTotal;
                opportunity.rawLaborBudget = rawLaborBudget;
                opportunity.laborBurdenBudget = laborBurdenBudget;
                opportunity.laborBudget = laborBudget;
                opportunity.laborBudgetPercent = laborBudgetPercent;

                opportunity.cogs = materialBudget + laborBudget + opportunity.commission;

                opportunity.gross = newTotal - opportunity.cogs;
                opportunity.grossPercent = opportunity.gross / newTotal;
                opportunity.daysBudget = roundTo1(price.overhead / projectPrice.variables.dailyOH);

                // hrs calc
                opportunity.rawEstimatedHrs =
                    allWorkOrders?.reduce((sum, item) => {
                        return sum + (item?.ttlHours || 0);
                    }, 0) || 0;
                opportunity.ttlHours = opportunity.rawEstimatedHrs || 0;
                // adding tavel time to estimate hrs
                opportunity.ttlHours += price?.travelFee / price?.travelHrlyRate || 0;
                opportunity.ttlHours += extras.hours + extras.ply * 0.5;
                // adding extra time for change order labor into estimate hrs (lSubtotal/ total hrs + travel hrs)
                const workTaskCost =
                    opp?.changeOrders
                        ?.filter((item) => !item?.deleted) // Exclude deleted items
                        ?.reduce((sum, item) => sum + (item?.rawLaborCost || 0), 0) || 0;
                opportunity.ttlHours += opportunity.manHours;
                if (workTaskCost) {
                    opportunity.ttlHours += workTaskCost / (price.lSubtotal / opportunity.rawEstimatedHrs);
                }

                // grossProfit calculation
                const actualPrice = order?.actualTotals?.actualPrice ?? 0;
                const comm = opportunity.modifiedCommission; //actual commission paid
                // order.priceTotals.jobTotal * 0.1;
                const materials = order?.actualTotals?.actualMatCost ?? 0;
                const laborCost = order?.actualTotals?.actualLaborCost ?? 0;
                const subCost = order?.actualTotals?.subcontractorCost ?? 0;
                const laborBurden = laborCost * projectPrice.variables.ttlBurden;
                const labor = laborCost + laborBurden + subCost;

                grossProfit = actualPrice - comm - materials - labor;
            }
            // for volume adding billable & change order
            project.realRevBudget = realRevBudget;
            project.salePriceBreakdown = {
                originalEstimate: salePrice,
                billableHours: totalHoursCost,
                rrPlywood: totalPlyCost,
                addMaterial: totalMatCost,
                changeOrder: opp?.changeOrderValue,
            };
            project.salePrice = newTotal;
            project.opportunity = opportunity;
            project.grossProfit = grossProfit;
            project.hrs = hrs || 0;
            project.cost = cost || 0;
            project.totalLeadBonus = totalLeadBonus || 0;
            project.pieceWorkData = pieceWorkData || [];
            project.dates = dates || [];
            project.days = days;

            opportunity.order = { ...opportunity.order, workOrder: [...allWorkOrders] };

            // Crew Lead Bonus
            project.leadIndex = leadIndex || 0;

            project.totalTasks = totalTasks || {};
            project.laborReport = laborReport || [];

            return project;
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async crewProjectReport(companyId: string, oppId: string) {
        try {
            const [pwSettingAllData, workTaskAllData, timeCards, oppData, variables, crews] =
                await Promise.all([
                    this.pieceWorkSettingModel
                        .find({
                            companyId: companyId,
                        })
                        .sort({ usesPitch: -1, isExtra: 1, sequence: 1 }),
                    this.workTaskModel.find({ companyId }),
                    this.crewService.getTimeCardsForQuery({
                        projectId: oppId, //in time card opp is project
                        deleted: { $ne: true },
                        active: { $ne: true },
                    }),
                    this.opportunityModel
                        .findOne({ _id: oppId, companyId })
                        .populate("contactId", "fullName businessName isBusiness", "Contact")
                        .populate("stage", "stageGroup", "CrmStage")
                        .populate("orderId", null, "Order"),
                    this.companySettingModel.findOne({ companyId }),
                    this.crewModel.aggregate([
                        {
                            $match: {
                                companyId,
                            },
                        },
                        {
                            $lookup: {
                                from: "CrewMember",
                                localField: "_id",
                                foreignField: "crewId",
                                as: "members",
                            },
                        },
                    ]),
                ]);

            // using any type for undefined value in schema
            const opp: any = oppData;

            const timeIds = timeCards.map((t) => t._id);

            const pieceWork = await this.pieceWorkModel.find({
                // projectId: roofProject.oppId,
                timeCardId: { $in: timeIds },
                companyId,
                deleted: { $ne: true },
            });
            timeCards.map((card: any) => {
                const work = pieceWork.find((wrk) => wrk.timeCardId === card._id);
                card.work = work;
            });

            // Initialize the map and sequence in a single reduce function
            const { pwSettingMap, pwSettingSequence } = pwSettingAllData.reduce(
                (acc, setting) => {
                    const { _id, workTask, name } = setting;

                    // Create the map of settings
                    acc.pwSettingMap[_id] = setting;

                    // Initialize the sequence for each workTask
                    if (!acc.pwSettingSequence[workTask]) {
                        acc.pwSettingSequence[workTask] = { currentSequence: 1 };
                    } else {
                        acc.pwSettingSequence[workTask].currentSequence++;
                    }

                    // Assign the current sequence to the task name
                    acc.pwSettingSequence[workTask][name] = acc.pwSettingSequence[workTask].currentSequence;

                    return acc;
                },
                { pwSettingMap: {}, pwSettingSequence: {} },
            );

            // Remove the tracking property (currentSequence) from the final structure
            const finalPwSettingSequence = Object.entries(pwSettingSequence).reduce(
                (acc, [workTask, data]) => {
                    acc[workTask] = Object.fromEntries(
                        Object.entries(data).filter(([key]) => key !== "currentSequence"),
                    );
                    return acc;
                },
                {},
            );

            const workTaskSequencemapping = workTaskAllData.reduce((map, workTask) => {
                map[workTask._id] = workTask?.sequence;
                return map;
            }, {});

            timeCards.forEach((card) => {
                card?.work?.work?.workDone.forEach((pieceWork) => {
                    const setting = pwSettingMap[pieceWork.id];
                    if (setting) {
                        // Adding name and unit to pieceWork
                        pieceWork.name = setting.name;
                        pieceWork.unit = setting.unit.split(" (")[0];
                    }
                });
            });

            const workerArray = timeCards.map((card) => card.memberId);
            const workers: any = await this.memberModel.aggregate([
                {
                    $match: {
                        company: companyId,
                        _id: { $in: workerArray },
                    },
                },
                {
                    $lookup: {
                        from: "Compensation",
                        localField: "_id",
                        foreignField: "memberId",
                        as: "wage",
                    },
                },
                {
                    $unwind: {
                        path: "$wage",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $addFields: {
                        name: {
                            $cond: {
                                if: {
                                    $and: [
                                        { $ifNull: ["$preferredName", false] },
                                        { $ne: ["$preferredName", ""] },
                                    ],
                                },
                                then: {
                                    $concat: [
                                        "$preferredName",
                                        " ",
                                        {
                                            $arrayElemAt: [{ $split: ["$name", " "] }, 1],
                                        },
                                    ],
                                },
                                else: "$name",
                            },
                        },
                    },
                },
            ]);

            let hrs = 0;
            let cost = 0;
            let totalLeadBonus = 0;
            let pieceWorkData = [];
            let dates = [];
            // Crew Lead Bonus
            let leadIndex = 0;

            const crewLeadEarningsArray = [];
            // Create an array to hold days the crew lead was off and won't get the lead bonus

            const totalTasks = {};
            const laborReport = [];
            for (const [idx, worker] of workers.entries()) {
                worker.pieceWork = [];
                worker.pwHourly = 0;
                worker.earned = 0;
                worker.hrs = 0;
                worker.leadEarnings = 0;
                worker.pieceWorkEarned = 0;

                let salaryHourly = 0;

                const salaryDates: { workTask: string; date: string }[] = [];
                const travelDates: { workTask: string; date: string }[] = [];

                const hoursPerDay = [];
                const workerCards = timeCards.filter((card) => card.memberId === worker._id);
                worker.cards = [];

                for (const card of workerCards) {
                    //to calculate total task type hrs on project
                    const workTaskData = workTaskAllData.find((workTask) => workTask._id === card.task);
                    const taskType = workTaskData?.name.toLowerCase();
                    totalTasks[taskType] = (totalTasks[taskType] || 0) + (card?.hrs || 0);

                    worker.currWage = findCurrentWage(worker?.wage?.wageHistory, new Date(card.timeIn));

                    worker.salaried =
                        worker.currWage && worker?.currWage?.wageInterval === WageIntervalEnum.Year
                            ? true
                            : false;
                    worker.hourlyWage =
                        worker.currWage && worker?.currWage?.wageInterval === WageIntervalEnum.Hour
                            ? worker?.currWage?.wageAmount
                            : 0;

                    // worker salary
                    salaryHourly = worker.salaried
                        ? worker.currWage.wageAmount / (52 * 5 * 8)
                        : worker.hourlyWage || 0;

                    let crewLead = false;
                    let activeCrew;
                    for (const crew of crews) {
                        if (!crew.members.length) continue;
                        // Figure out which crew this person is a member of
                        for (const member of crew.members) {
                            if (
                                worker._id === member.memberId &&
                                member.startDate <= card.timeIn &&
                                (!member.removeDate || member.removeDate > card.timeIn)
                            ) {
                                activeCrew = crew;
                            }
                        }
                    }

                    const crewLeadId = findCrewLeadId(activeCrew, new Date(card.timeIn));

                    // Is this person the crew lead of that day?
                    crewLead = worker._id === crewLeadId;

                    if (crewLead) leadIndex = idx;
                    const work = card.work;
                    const hrs = card?.hrs || 0;
                    worker.pwHourly += work?.hourlyEarnings || 0;
                    if (work?.work) {
                        const workTaskId = work.task;
                        const earned = Number(work.earned) || 0;
                        const { hourlyEarnings } = work;
                        const { workDone, extraWorkTime } = work?.work;
                        const hourlyWages = work.hourlyWages;

                        if (
                            worker.pieceWork.find((item) => item.workTask === `Total Hourly Earning`) ===
                            undefined
                        ) {
                            worker.pieceWork.push({
                                workTask: `Total Hourly Earning`,
                                pieceWork: 0,
                            });
                        }
                        // Update Total Hourly Earning
                        worker.pieceWork.find((item) => item.workTask === `Total Hourly Earning`).pieceWork +=
                            hourlyWages;

                        // Initialize variables to store the key for extra work time
                        let extraWorkKey = "Extra Hour";

                        // If workTaskData.pieceWork is not available, update extraWorkKey
                        if (workTaskData && !workTaskData?.pieceWork) {
                            extraWorkKey = "Other Hour";
                        }

                        // Find index of existing workTaskId or add a new entry
                        let existingTaskIndex = worker.pieceWork.findIndex(
                            (item) => item.workTask === workTaskId,
                        );

                        if (existingTaskIndex === -1) {
                            existingTaskIndex = worker.pieceWork.length;
                            worker.pieceWork.push({
                                workTask: workTaskId,
                                taskName: workTaskData?.name,
                                extraHours: extraWorkTime || 0,
                                hrs,
                                hourlyEarnings,
                                pieceWork: [],
                                salaryHourly: roundTo2(hrs * salaryHourly),
                            });
                        } else {
                            worker.pieceWork[existingTaskIndex].extraHours += extraWorkTime;
                            worker.pieceWork[existingTaskIndex].hourlyEarnings += hourlyEarnings;
                            worker.pieceWork[existingTaskIndex].hrs += hrs;
                            worker.pieceWork[existingTaskIndex].salaryHourly += roundTo2(hrs * salaryHourly);
                        }

                        // Iterate through workDone and update pieceWork
                        workDone.forEach((pwSetting) => {
                            // calculating piece work cost for individual
                            const { id, earned } = pwSetting;

                            const existingObjectIndex = worker.pieceWork[
                                existingTaskIndex
                            ].pieceWork.findIndex((obj) => obj.name === pwSetting.name);

                            if (existingObjectIndex !== -1) {
                                // If an object with the same name exists
                                const existingObject =
                                    worker.pieceWork[existingTaskIndex].pieceWork[existingObjectIndex];

                                // Check if pitch and layers already exist
                                const existingPitchLayersIndex = existingObject.workDone.findIndex(
                                    (obj) => obj.pitch === pwSetting.pitch && obj.layers === pwSetting.layers,
                                );

                                if (existingPitchLayersIndex !== -1) {
                                    // If both pitch and layers exist, update their squares
                                    existingObject.workDone[existingPitchLayersIndex].value +=
                                        Number(pwSetting.amount) || 0;
                                    existingObject.workDone[existingPitchLayersIndex].cost += earned;
                                } else {
                                    // If either pitch or layers don't exist, push new data
                                    const dataToPush = {
                                        pitch: pwSetting.pitch,
                                        layers: pwSetting.layers,
                                        value: Number(pwSetting.amount),
                                        unit: pwSetting.unit,
                                        cost: earned,
                                    };
                                    existingObject.workDone.push(dataToPush);
                                }
                            } else {
                                // If the object doesn't exist, push a new object with name, value, and workDone array
                                const dataToPush = {};
                                dataToPush["cost"] = earned;

                                if (pwSetting.pitch !== undefined) {
                                    dataToPush["pitch"] = pwSetting.pitch;
                                }

                                if (pwSetting.layers !== undefined) {
                                    dataToPush["layers"] = pwSetting.layers;
                                }

                                if (pwSetting.amount !== undefined) {
                                    dataToPush["value"] = Number(pwSetting.amount);
                                }

                                if (pwSetting.unit !== undefined) {
                                    dataToPush["unit"] = pwSetting.unit;
                                }

                                // Check if any of the properties exist before pushing the new object
                                if (Object.keys(dataToPush).length > 0) {
                                    worker.pieceWork[existingTaskIndex].pieceWork.push({
                                        name: pwSetting.name,
                                        workDone: [dataToPush],
                                    });
                                }
                            }
                        });

                        // Add extraWorkTime to the appropriate key
                        if (worker.pieceWork.find((item) => item.workTask === extraWorkKey) === undefined) {
                            // If "Extra Hour" or "Other Hour" not present, initialize with 0
                            worker.pieceWork.push({
                                workTask: extraWorkKey,
                                pieceWork: 0,
                            });
                        }
                        // Update extra work time
                        worker.pieceWork.find((item) => item.workTask === extraWorkKey).pieceWork +=
                            extraWorkTime;

                        // Ensure "Other Hour" and "Extra Hour" have a default value of 0
                        if (
                            worker.pieceWork.find((item) => item.workTask === extraWorkKey).pieceWork ===
                            undefined
                        ) {
                            worker.pieceWork.find((item) => item.workTask === extraWorkKey).pieceWork = 0;
                        }

                        if (
                            worker.pieceWork.find(
                                (item) => item.workTask === `${workTaskData?.name} Cost`,
                            ) === undefined
                        ) {
                            // If `${workTaskData?.name} Cost` not present, initialize with 0
                            worker.pieceWork.push({
                                workTask: `${workTaskData?.name} Cost`,
                                pieceWork: 0,
                            });
                        }
                        // Update earned cost
                        worker.pieceWork.find(
                            (item) => item.workTask === `${workTaskData?.name} Cost`,
                        ).pieceWork += earned;
                    }

                    if (!crewLead && workTaskData?.showOnScoreboard) {
                        const leadData = workers.find((w) => w._id === crewLeadId);
                        const crewPieceWork =
                            findCurrentWage(leadData?.wage?.wageHistory, new Date(card.timeIn))
                                ?.crewPieceWork || 0;
                        const item = {
                            date: card.timeIn,
                            lead: crewLeadId,
                            workTask: workTaskData._id,
                            earned: card.removeFromLead
                                ? 0
                                : (work && roundTo2(work.earned * crewPieceWork)) || 0,
                        };
                        crewLeadEarningsArray.push(item);
                    }
                    hoursPerDay.push({
                        date: shortenDate(card.timeIn),
                        hours: card.hrs,
                    });

                    // not using weekend check as the salary/hourly is always counted on weekends as well
                    // const weekend = isWeekend(variables.weekEndDays, card.timeIn);

                    // Only count salary on weekdays
                    // if (!weekend) salaryDates.push(shortenDate(card.timeIn));

                    salaryDates.push({ workTask: workTaskData._id, date: shortenDate(card.timeIn) });

                    // Only count travel on Roofing & Tear Off
                    if (
                        workTaskData?.pieceWork &&
                        workTaskData.addTravel &&
                        card.work?.work?.extraWorkTime !== card.hrs
                    ) {
                        travelDates.push({ workTask: workTaskData._id, date: shortenDate(card.timeIn) });
                    }
                    worker.earned += work?.earned || 0;
                    worker.hrs += card.hrs;
                    dates.push(shortenDate(card.timeIn));

                    worker.cards.push(card);
                }

                // Calculate salary and travel
                const uniqueSDates = dedupePwObjects(salaryDates);
                const uniqueTDates = dedupePwObjects(travelDates);

                const indicesToDelete: number[] = [];
                for (let i = 0; i < worker.pieceWork.length; i++) {
                    const pieceWorkItem = worker.pieceWork[i];
                    const { workTask, taskName, extraHours } = pieceWorkItem;
                    let { pieceWork } = pieceWorkItem;
                    pieceWork = JSON.parse(JSON.stringify(pieceWork));
                    if (workTask === "Total Hourly Earning") {
                        const existingTaskIndex = pieceWorkData.findIndex(
                            (item) => item.workTask === workTask,
                        );
                        if (existingTaskIndex !== -1) {
                            // If the workTask already exists, update its value
                            pieceWorkData[existingTaskIndex].pieceWork = roundTo2(
                                pieceWorkData[existingTaskIndex].pieceWork + pieceWork,
                            );
                        } else {
                            // If the workTask doesn't exist, add a new entry
                            pieceWorkData.push({
                                workTask,
                                pieceWork: roundTo2(pieceWork),
                            });
                        }
                        indicesToDelete.push(i);
                    } else if (workTask === "Extra Hour" || workTask === "Other Hour") {
                        const existingTaskIndex = pieceWorkData.findIndex(
                            (item) => item.workTask === workTask,
                        );
                        if (existingTaskIndex !== -1) {
                            // If the workTask already ex ists, update its value
                            pieceWorkData[existingTaskIndex].pieceWork = roundTo2(
                                pieceWorkData[existingTaskIndex].pieceWork + pieceWork,
                            );
                        } else {
                            // If the workTask doesn't exist, add a new entry
                            pieceWorkData.push({
                                workTask,
                                pieceWork: roundTo2(pieceWork),
                            });
                        }
                        indicesToDelete.push(i);
                    } else if (/Cost$/.test(workTask)) {
                        const existingTaskIndex = pieceWorkData.findIndex(
                            (item) => item.workTask === workTask,
                        );
                        if (existingTaskIndex !== -1) {
                            // If the workTask already exists, update its value
                            pieceWorkData[existingTaskIndex].pieceWork += pieceWork;
                        } else {
                            // If the workTask doesn't exist, add a new entry
                            pieceWorkData.push({
                                workTask,
                                pieceWork,
                            });
                        }
                        if (pieceWork) {
                            worker.pieceWorkEarned += roundTo2(pieceWork) > 0 ? roundTo2(pieceWork) : 0;
                        }
                    } else {
                        // Check if the workTask already exists in pieceWorkData
                        const existingTaskIndex = pieceWorkData.findIndex(
                            (item) => item.workTask === workTask,
                        );
                        if (existingTaskIndex !== -1) {
                            // If the workTask already exists, update its data
                            const existingPieceWork = pieceWorkData[existingTaskIndex].pieceWork;
                            pieceWorkData[existingTaskIndex].extraHours += extraHours;
                            // Iterate over pieceWork of the worker
                            for (const pieceWorkObj of pieceWork) {
                                const existingObjectIndex = existingPieceWork.findIndex(
                                    (obj) => obj.name === pieceWorkObj.name,
                                );

                                if (existingObjectIndex !== -1) {
                                    // If the name already exists, check for pitch and layers
                                    const existingObject = existingPieceWork[existingObjectIndex];

                                    for (const pwObj of pieceWorkObj.workDone) {
                                        const existingWorkDoneIndex = existingObject.workDone.findIndex(
                                            (existingWorkDone) =>
                                                (pwObj.pitch === undefined ||
                                                    existingWorkDone.pitch === pwObj.pitch) &&
                                                (pwObj.layers === undefined ||
                                                    existingWorkDone.layers === pwObj.layers),
                                        );
                                        if (pwObj.pitch === undefined && pwObj.layers === undefined) {
                                            // If both pitch and layers are undefined in pwObj
                                            if (existingWorkDoneIndex !== -1) {
                                                existingObject.workDone[existingWorkDoneIndex].value +=
                                                    pwObj.value;
                                            }
                                        } else {
                                            // If either pitch or layers are defined in pwObj
                                            if (existingWorkDoneIndex !== -1) {
                                                // If existingWorkDoneIndex is found, update the value
                                                existingObject.workDone[existingWorkDoneIndex].value +=
                                                    pwObj.value;
                                            } else {
                                                // If existingWorkDoneIndex is not found, push new data
                                                const dataToPush = {};

                                                if (pwObj.pitch !== undefined) {
                                                    dataToPush["pitch"] = pwObj.pitch;
                                                }

                                                if (pwObj.layers !== undefined) {
                                                    dataToPush["layers"] = pwObj.layers;
                                                }

                                                if (pwObj.value !== undefined) {
                                                    dataToPush["value"] = pwObj.value;
                                                }

                                                if (pwObj.unit !== undefined) {
                                                    dataToPush["unit"] = pwObj.unit;
                                                }

                                                // If either pitch or layers don't exist, push new data
                                                if (Object.keys(dataToPush).length > 0) {
                                                    existingObject.workDone.push(dataToPush);
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    // Otherwise, push the new object
                                    existingPieceWork.push(pieceWorkObj);
                                }
                            }
                        } else {
                            // If the workTask doesn't exist, add a new entry
                            pieceWorkData.push({
                                workTask,
                                taskName,
                                extraHours,
                                pieceWork: pieceWork.map((obj) => ({
                                    ...obj,
                                    workDone: obj.workDone,
                                })), // Make a copy of pieceWork
                            });
                        }
                    }
                }

                // Salary calc
                worker.salary = 0;

                worker.dates = uniqueSDates.map(({ workTask, date }) => {
                    const obj: any = {};
                    obj.date = date;
                    obj.hours = 0;
                    obj.regHours = 0;
                    obj.otHours = 0;
                    let daySalary = 0;
                    hoursPerDay.map((day) => {
                        if (day.date === date) obj.hours += day.hours;
                    });
                    if (obj.hours > 8) {
                        obj.regHours = 8;
                        obj.otHours = obj.hours - 8;
                    } else {
                        obj.regHours = obj.hours;
                    }
                    if (worker.salaried) daySalary = roundTo2(obj.regHours * salaryHourly);
                    if (worker.hourlyWage)
                        daySalary = roundTo2(obj.regHours * salaryHourly + obj.otHours * salaryHourly);
                    worker.salary += daySalary;
                    worker.earned += daySalary;
                    const existingTaskIndex = pieceWorkData.findIndex((item) => item.workTask === workTask);

                    if (existingTaskIndex !== -1) {
                        pieceWorkData[existingTaskIndex] = {
                            ...pieceWorkData[existingTaskIndex],
                            cost: (pieceWorkData[existingTaskIndex].cost || 0) + daySalary, // Add daySalary to the current cost
                            salaryEarned: (pieceWorkData[existingTaskIndex].salaryEarned || 0) + daySalary, // Update salaryEarned with daySalary
                        };
                    }
                    return obj;
                });

                // Travel calc
                worker.travel = 0;
                worker.travelDays = 0;
                if (opp?.distance && Number(opp?.distance) >= 20) {
                    const pwFee = roundTo2(Number(opp.distance) * variables.travelFee);
                    const salFee = roundTo2(Number(opp.distance) * (variables.travelFee / 2));

                    for (const date of uniqueTDates) {
                        const dayTravel = worker.salaried || worker.hourlyWage ? salFee : pwFee;
                        worker.travel += dayTravel;
                        worker.travelDays++;
                        worker.earned += dayTravel;

                        const existingTaskIndex = pieceWorkData.findIndex(
                            (item) => item.workTask === date.workTask,
                        );

                        if (existingTaskIndex !== -1) {
                            pieceWorkData[existingTaskIndex] = {
                                ...pieceWorkData[existingTaskIndex],
                                cost: (pieceWorkData[existingTaskIndex].cost || 0) + dayTravel, // Add dayTravel to the current cost
                                travelEarned:
                                    (pieceWorkData[existingTaskIndex].travelEarned || 0) + dayTravel, // Update travelEarned with dayTravel
                            };
                        }
                    }
                }

                // Remove items from the array based on the tracked indices
                for (let i = indicesToDelete.length - 1; i >= 0; i--) {
                    worker.pieceWork.splice(indicesToDelete[i], 1);
                }

                // Create a map of costs by task name
                const costMap = worker.pieceWork.reduce((acc, { workTask, pieceWork }) => {
                    if (workTask.endsWith(" Cost")) {
                        acc[workTask.replace(" Cost", "")] = pieceWork;
                    }
                    return acc;
                }, {});

                // Update, filter, and add hours to the worker.pieceWork
                worker.pieceWork = worker.pieceWork
                    .filter(({ workTask }) => !workTask.endsWith(" Cost"))
                    .map((item) => {
                        const updatedItem =
                            isUUID(item.workTask) && item.taskName in costMap
                                ? {
                                      ...item,
                                      cost: costMap[item.taskName] + (item?.salaryHourly || 0),
                                  }
                                : item;

                        return updatedItem;
                    });

                worker.pieceWork.forEach(({ workTask, pieceWork }) => {
                    if (isUUID(workTask)) {
                        pieceWork.sort((a, b) => {
                            const sequenceA =
                                finalPwSettingSequence[workTask]?.[a.name] ?? Number.MAX_SAFE_INTEGER;
                            const sequenceB =
                                finalPwSettingSequence[workTask]?.[b.name] ?? Number.MAX_SAFE_INTEGER;
                            return sequenceA - sequenceB;
                        });
                    }
                });

                worker.pieceWork.sort((a, b) => {
                    const sequenceA = workTaskSequencemapping[a.workTask] ?? Number.MAX_SAFE_INTEGER;
                    const sequenceB = workTaskSequencemapping[b.workTask] ?? Number.MAX_SAFE_INTEGER;
                    return sequenceA - sequenceB;
                });
                worker.pieceWorkEarned -= worker.pwHourly;
                hrs += worker.hrs;
                cost += worker.earned;
                laborReport.push(worker);
            }

            // Apply crew lead earnings to proper person
            crewLeadEarningsArray.map((item) => {
                workers.map((worker) => {
                    if (worker._id === item.lead) {
                        worker.leadEarnings += item.earned;
                        worker.earned += item.earned;
                        totalLeadBonus += roundTo2(item.earned) || 0;
                        cost += item.earned;

                        const existingTaskIndex = pieceWorkData.findIndex(
                            (pw) => pw.workTask === item.workTask,
                        );

                        if (existingTaskIndex !== -1) {
                            pieceWorkData[existingTaskIndex] = {
                                ...pieceWorkData[existingTaskIndex],
                                cost: (pieceWorkData[existingTaskIndex].cost || 0) + roundTo2(item.earned), // Add leadEarned to the current cost
                                leadEarned:
                                    (pieceWorkData[existingTaskIndex].leadEarned || 0) +
                                    roundTo2(item.earned), // Update leadEarned with daySalary
                            };
                        }
                    }
                });
            });

            const order = opp?.orderId;
            const allWorkOrders = [];
            const estimateBudget = {};
            let totalEstimateBudget = 0;
            if (order) {
                order?.projects?.forEach(({ workOrder }) => {
                    allWorkOrders.push(...workOrder);
                });

                allWorkOrders.forEach((workOrder) => {
                    const { cost, ttlHours, worker } = workOrder;
                    if (!estimateBudget[worker]) {
                        estimateBudget[worker] = { cost: 0, hours: 0 };
                    }
                    estimateBudget[worker].cost += cost;
                    estimateBudget[worker].hours += ttlHours;
                    totalEstimateBudget += cost;
                });
                // adding travel amount in equal ratio for esimated task
                Object.keys(estimateBudget).forEach((key) => {
                    estimateBudget[key].cost += roundTo2(
                        order?.priceTotals?.travelFee * (estimateBudget[key].cost / totalEstimateBudget),
                    );
                    //TODO: hrs to check
                    estimateBudget[key].hours += roundTo2(
                        (order?.priceTotals?.travelFee * (estimateBudget[key].cost / totalEstimateBudget)) /
                            order?.priceTotals?.travelHrlyRate,
                    );
                });
            }

            // Create a map of costs by task name
            const costMap = pieceWorkData.reduce((acc, { workTask, pieceWork }) => {
                if (workTask.endsWith(" Cost")) {
                    acc[workTask.replace(" Cost", "")] = pieceWork;
                }
                return acc;
            }, {});

            // adding missing peicework task from order if not on timecards
            Object.keys(estimateBudget).forEach((key) => {
                const data = pieceWorkData.find((p) => p.workTask === key);
                if (!data)
                    pieceWorkData.push({
                        workTask: key,
                        taskName: workTaskAllData.find((w) => w._id === key)?.name || "",
                        extraHours: 0,
                        pieceWork: [],
                        salaryEarned: 0,
                        leadEarned: 0,
                        cost: 0,
                        estimateCost: estimateBudget[key]?.cost || 0,
                        estimateHrs: estimateBudget[key]?.hours || 0,
                        hrs: 0,
                    });
            });

            // Update, filter, and add hours to the pieceWorkData
            pieceWorkData = pieceWorkData
                .filter(({ workTask }) => !workTask.endsWith(" Cost"))
                .map((item) => {
                    const updatedItem =
                        isUUID(item.workTask) && item.taskName in costMap
                            ? {
                                  ...item,
                                  cost: (item.cost || 0) + costMap[item.taskName],
                                  estimateCost: estimateBudget[item.workTask]?.cost || 0,
                                  estimateHrs: estimateBudget[item.workTask]?.hours || 0,
                              }
                            : item;

                    const taskNameLower = updatedItem.taskName?.toLowerCase();
                    return taskNameLower && taskNameLower in totalTasks
                        ? { ...updatedItem, hrs: totalTasks[taskNameLower] }
                        : updatedItem;
                });

            // Sort pieceWork data based on the sequences defined in cleanPwSettingSequence
            pieceWorkData.forEach(({ workTask, pieceWork }) => {
                if (isUUID(workTask)) {
                    pieceWork.sort((a, b) => {
                        const sequenceA =
                            finalPwSettingSequence[workTask]?.[a.name] ?? Number.MAX_SAFE_INTEGER;
                        const sequenceB =
                            finalPwSettingSequence[workTask]?.[b.name] ?? Number.MAX_SAFE_INTEGER;
                        return sequenceA - sequenceB;
                    });
                }
            });

            pieceWorkData.sort((a, b) => {
                const sequenceA = workTaskSequencemapping[a.workTask] ?? Number.MAX_SAFE_INTEGER;
                const sequenceB = workTaskSequencemapping[b.workTask] ?? Number.MAX_SAFE_INTEGER;
                return sequenceA - sequenceB;
            });

            dates = dedupeArray(dates);
            const days = dates.length;
            let customPo;
            if (!oppData?.PO) {
                customPo = await this.customProjectModel.findOne({ _id: oppId });
            }
            return {
                hrs,
                cost,
                totalLeadBonus,
                pieceWorkData,
                leadIndex,
                totalTasks,
                dates,
                days,
                laborReport,
                po: oppData?.PO || customPo?.PO || "",
                num: oppData?.num || customPo?.num || "",
                allWorkOrders,
            };
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async crewScoreboard(userId: string, companyId: string, startDate: Date, endDate: Date) {
        try {
            const reportStart = new Date(startDate);
            const reportEnd = new Date(endDate);

            const [crews, workTaskAllData, pwSettingAllData] = await Promise.all([
                this.crewModel
                    .aggregate([
                        {
                            $match: {
                                companyId,
                                deleted: { $ne: true },
                                startDate: { $lte: reportEnd },
                                $or: [{ endDate: { $exists: false } }, { endDate: { $gte: reportStart } }],
                            },
                        },
                        {
                            $lookup: {
                                from: "CrewMember",
                                // pipeline: [{ $match: { deleted: false } }],
                                pipeline: [
                                    {
                                        $sort: { startDate: 1 },
                                    },
                                ],
                                localField: "_id",
                                foreignField: "crewId",
                                as: "members",
                            },
                        },
                    ])
                    .sort({ order: 1 }),
                this.workTaskModel.find({
                    companyId,
                }),
                this.pieceWorkSettingModel.find({
                    companyId: companyId,
                }),
            ]);

            const memberIds: string[] = [];

            crews.forEach((crew) => {
                crew.members.forEach((member) => {
                    memberIds.push(member.memberId);
                });
            });

            const [users, allTimeCards, allWageHistory] = await Promise.all([
                this.memberModel.find({
                    _id: { $in: memberIds },
                    deleted: false,
                }),

                this.crewService.getTimeCardsForQuery({
                    deleted: { $ne: true },
                    memberId: { $in: memberIds },
                    timeIn: { $gte: reportStart, $lte: reportEnd },
                }),
                this.compensationModel.find({
                    companyId,
                    memberId: { $in: memberIds },
                }),
            ]);

            const timeIds = allTimeCards.map((t) => t._id);

            const salesComm = await this.payModel.findOne({ companyId });
            const versionMapping = salesComm.pieceWork.versions.reduce((acc, version) => {
                acc[version._id] = version.name;
                return acc;
            }, {});

            const pwSettingMap = pwSettingAllData.reduce((map, setting) => {
                map[setting._id] = setting;
                return map;
            }, {});

            const pieceWork = await this.pieceWorkModel.find({
                companyId,
                timeCardId: { $in: timeIds },
                deleted: false,
            });

            pieceWork.forEach((pw) => {
                pw?.work?.workDone.forEach((pieceWork) => {
                    const setting = pwSettingMap[pieceWork.id];
                    if (setting) {
                        // Adding name and unit to pieceWork
                        pieceWork.name = setting.name;
                        pieceWork.unit = setting.unit.split(" (")[0];
                    }
                });
            });

            const activeCrews = [];

            for (const crew of crews) {
                // Crew info from timesheets
                let cPoints = 0;
                let cHours = 0;
                const crewMembers = [];

                if (!crew.members.length) continue;
                for (const member of crew.members) {
                    if (
                        (member?.removeDate && new Date(member.removeDate) <= reportStart) ||
                        new Date(member.startDate) >= reportEnd
                    ) {
                        // member not active
                        continue;
                    } else {
                        // Check if member is salary or piece work
                        // Pull user info and check type of piece work
                        let versionId;
                        const user = users.find((u) => u._id === member.memberId);

                        // Day collections
                        const mDays = [];
                        let mPoints = 0;
                        let mHours = 0;
                        // Set new date objects
                        const newStart = new Date(reportStart);
                        const newEnd = new Date(reportEnd);
                        // Count through each day
                        for (let i = newStart; i < newEnd; i.setDate(i.getDate() + 1)) {
                            const start = new Date(i);
                            const end = new Date(i);
                            end.setDate(end.getDate() + 1);
                            end.setMilliseconds(end.getMilliseconds() - 1);

                            //find compensation for member on current date
                            const userCompensation = allWageHistory.find(
                                (w) => w.memberId === member.memberId,
                            );
                            const compensationData = findCurrentWage(userCompensation?.wageHistory, start);
                            // setting version id
                            versionId = compensationData?.versionId;

                            // const weekend = isWeekend(companyWeekend.weekEndDays, start);

                            if (new Date(user?.hireDate) > end) continue;
                            if (user?.terminateDate && new Date(user.terminateDate) < start) continue;

                            const dayObj = {
                                date: start,
                                cards: [],
                                points: 0,
                                hours: 0,
                                ptsPerHour: 0,
                            };
                            const timeCards: TimeCard[] = allTimeCards
                                .filter((t) => t.memberId === member.memberId)
                                .filter((t) => new Date(t.timeIn) >= start && new Date(t.timeIn) <= end)
                                .sort((a, b) => a.timeIn.getTime() - b.timeIn.getTime());

                            for (const card of timeCards) {
                                // Skip active cards
                                if (card.active) continue;
                                if (
                                    card.memberId === member.memberId &&
                                    new Date(card.timeIn) >= start &&
                                    new Date(card.timeIn) <= end
                                ) {
                                    card["work"] = {};
                                    card["work"] = pieceWork.find((work) => work.timeCardId === card._id);
                                    if (!card["work"]) continue;
                                    dayObj.cards.push(card);
                                    dayObj.hours += roundTo2(card.hrs);
                                    mHours += roundTo2(card.hrs);
                                    cHours += roundTo2(card.hrs);
                                    const workCard = card["work"]?.work;
                                    card["points"] = 0;
                                    const workTaskData = workTaskAllData.find(
                                        (workTask) => workTask._id === card?.task,
                                    );

                                    if (workTaskData.showOnScoreboard) {
                                        for (const pieceWork of workCard?.workDone) {
                                            card["points"] += roundTo1(pieceWork?.earned);
                                        }
                                        if (workCard?.extraWorkTime) {
                                            // Define how many points per hourly work earnings
                                            //if member piece work is 12 then calculate based on 9 else 7.5
                                            const ptsFor1Hour =
                                                compensationData?.pieceWorkHourlyRate > 10 ? 9 : 7.5 || 9;

                                            card["points"] += roundTo1(workCard?.extraWorkTime * ptsFor1Hour);
                                        }

                                        dayObj.points += card["points"];
                                        mPoints += card["points"];
                                        cPoints += card["points"];
                                    }
                                }
                            }

                            dayObj.ptsPerHour = roundTo1(dayObj.points / dayObj.hours);
                            mDays.push(dayObj);
                        }

                        const mName = member.memberName.split(" ");

                        const memberReport = {
                            name: member.memberName,
                            lastName: mName[1],
                            memberId: member.memberId,
                            days: mDays,
                            mHours: roundTo2(mHours),
                            mPoints: roundTo1(mPoints),
                            mPtsPerHour: roundTo1(mPoints / mHours),
                            mVersion: {
                                versionId: versionId,
                                versionName: versionMapping[versionId],
                            },
                        };
                        crewMembers.push(memberReport);
                    }
                }
                const crewReport = {
                    name: crew.name,
                    crewId: crew._id,
                    order: crew.order,
                    crewMembers: crewMembers.sort(dynamicSort("lastName")),
                    cHours: roundTo2(cHours),
                    cPoints: roundTo1(cPoints),
                    cPtsPerHour: roundTo1(cPoints / cHours),
                };
                activeCrews.push(crewReport);
            }

            return new OkResponse({ activeCrews });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async commissionReport(
        userId: string,
        companyId: string,
        loginMemberId: string,
        teamPermission: PermissionsEnum,
        reportStart: Date,
        reportEnd: Date,
        monthStart: Date,
    ) {
        try {
            const query: any = {
                company: companyId,
                hireDate: { $lte: reportEnd },
                $or: [
                    { deleted: false },
                    {
                        $and: [
                            { terminateDate: { $exists: true } },
                            { terminateDate: { $gte: reportStart } },
                        ],
                    },
                ],
            };

            if (teamPermission === PermissionsEnum.Managed) {
                query.managerId = loginMemberId;
            } else if (teamPermission === PermissionsEnum.Self) {
                query._id = loginMemberId;
            }

            const [salesComm, projectTypes, salesPeople] = await Promise.all([
                this.payModel.findOne({ companyId }),
                this.projectTypeModel.find({
                    companyId,
                    deleted: false,
                }),
                this.memberModel.aggregate([
                    {
                        $match: query,
                    },
                    {
                        $lookup: {
                            from: "Compensation",
                            localField: "_id",
                            foreignField: "memberId",
                            as: "wage",
                        },
                    },
                    {
                        $unwind: {
                            path: "$wage",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Position",
                            localField: "wage.positionId",
                            foreignField: "_id",
                            as: "position",
                        },
                    },
                    {
                        $match: {
                            "position.symbol": {
                                $in: ["SalesManager", "SalesPerson", "GeneralManager"],
                            },
                        },
                    },
                    {
                        $project: {
                            position: 0,
                        },
                    },
                    {
                        $addFields: {
                            name: {
                                $cond: {
                                    if: {
                                        $and: [
                                            { $ifNull: ["$preferredName", false] },
                                            { $ne: ["$preferredName", ""] },
                                        ],
                                    },
                                    then: {
                                        $concat: [
                                            "$preferredName",
                                            " ",
                                            {
                                                $arrayElemAt: [{ $split: ["$name", " "] }, 1],
                                            },
                                        ],
                                    },
                                    else: "$name",
                                },
                            },
                        },
                    },
                ]),
            ]);

            const { benchmarks } = salesComm.sales;

            const salesPersonIdArray = salesPeople.map((data) => data._id);

            const report: any = {};
            report.dates = {
                start: reportStart,
                end: reportEnd,
            };
            report.salesPeople = [];
            // Pull opps, combine w/ order and project to get project type

            const [allOpps, oppModifiedComm] = await Promise.all([
                this.opportunityModel
                    .aggregate([
                        {
                            $match: {
                                companyId,
                                deleted: { $ne: true },
                                // status: { $ne: "inactive" },
                                salesPersonHistory: { $in: salesPersonIdArray },
                                $or: [
                                    {
                                        $and: [
                                            { saleDate: { $gte: monthStart } },
                                            { saleDate: { $lte: reportEnd } },
                                        ],
                                    },
                                    {
                                        $and: [
                                            { jobCompletedDate: { $gte: monthStart } },
                                            { jobCompletedDate: { $lte: reportEnd } },
                                        ],
                                    },
                                    {
                                        $and: [
                                            { jobStartedDate: { $gte: monthStart } },
                                            { jobStartedDate: { $lte: reportEnd } },
                                        ],
                                    },
                                ],
                            },
                        },
                        {
                            $set: {
                                oppType: {
                                    $cond: {
                                        if: {
                                            $and: [
                                                { $gt: ["$saleDate", null] },
                                                { $gt: ["$acceptedType", null] },
                                            ],
                                        },
                                        then: "$acceptedType",
                                        else: "$oppType",
                                    },
                                },
                            },
                        },
                        {
                            $lookup: {
                                from: "ProjectType",
                                foreignField: "_id",
                                localField: "acceptedType",
                                as: "pType",
                                pipeline: [{ $project: { _id: 1, name: 1, typeReplacement: 1 } }],
                            },
                        },
                        {
                            $unwind: {
                                path: "$pType",
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                        {
                            $lookup: {
                                from: "Contact",
                                pipeline: [
                                    {
                                        $project: {
                                            _id: 1,
                                            fullName: 1,
                                            businessName: 1,
                                            isBusiness: 1,
                                        },
                                    },
                                ],
                                foreignField: "_id",
                                localField: "contactId",
                                as: "contactId",
                            },
                        },
                        {
                            $unwind: {
                                path: "$contactId",
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                        {
                            $project: {
                                comments: 0,
                                activities: 0,
                                stepsChecklist: 0,
                                actions: 0,
                                nextAction: 0,
                                taxJurisdiction: 0,
                                companyId: 0,
                                companyLang: 0,
                                companyLat: 0,
                                dateReceived: 0,
                                distance: 0,
                                duration: 0,
                                editedBy: 0,
                                currDate: 0,
                                createdBy: 0,
                                createdAt: 0,
                                updatedAt: 0,
                                zip: 0,
                                __v: 0,
                                deleted: 0,
                                oppNotes: 0,
                                companyAddress: 0,
                                workingCrew: 0,
                                todoCheck: 0,
                                checkpointActivity: 0,
                            },
                        },
                        // {
                        //     $addFields: {
                        //         "order.matList": 0,
                        //         "order.workOrder": 0,
                        //         // "project.customData.rawData": 0,
                        //         "project.projectInputs": 0,
                        //         "project.userInputData": 0,
                        //     },
                        // },
                        { $sort: { createdAt: -1 } },
                    ])
                    .exec(),
                this.commissionModificationModel
                    .aggregate([
                        {
                            $match: {
                                companyId,
                                salesPersonId: { $in: salesPersonIdArray },
                                $and: [{ date: { $gte: monthStart } }, { date: { $lte: reportEnd } }],
                            },
                        },
                        {
                            $lookup: {
                                from: "Opportunity",
                                foreignField: "_id",
                                localField: "oppId",
                                as: "oppId",
                                pipeline: [
                                    {
                                        $project: {
                                            _id: 1,
                                            PO: 1,
                                            num: 1,
                                            contactId: 1,
                                            acceptedType: 1,
                                            stage: 1,
                                        },
                                    },
                                ],
                            },
                        },
                        {
                            $unwind: {
                                path: "$oppId",
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                        {
                            $lookup: {
                                from: "Contact",
                                pipeline: [
                                    {
                                        $project: {
                                            _id: 1,
                                            fullName: 1,
                                            businessName: 1,
                                            isBusiness: 1,
                                        },
                                    },
                                ],
                                foreignField: "_id",
                                localField: "oppId.contactId",
                                as: "contact",
                            },
                        },
                        {
                            $unwind: {
                                path: "$contact",
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                        {
                            $lookup: {
                                from: "ProjectType",
                                foreignField: "_id",
                                localField: "oppId.acceptedType",
                                as: "pType",
                                pipeline: [{ $project: { _id: 1, name: 1, typeReplacement: 1 } }],
                            },
                        },
                        {
                            $unwind: {
                                path: "$pType",
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                        // {
                        //     $lookup: {
                        //         from: "CrmStage",
                        //         foreignField: "_id",
                        //         localField: "oppId.stage",
                        //         as: "stage",
                        //         pipeline: [{ $project: { stageGroup: 1 } }],
                        //     },
                        // },
                        // {
                        //     $unwind: {
                        //         path: "$stage",
                        //         preserveNullAndEmptyArrays: true,
                        //     },
                        // },
                    ])
                    .exec(),
            ]);
            await Promise.all(
                salesPeople.map(async (user, idx) => {
                    const wage = findCurrentWage(user?.wage?.wageHistory, reportStart);
                    const commission = wage?.saleCommission ?? 0; // already in decimal no need to divide by 100
                    const useBenchmarkBonus = wage?.useBenchmarkBonus;

                    const opps = [];
                    const noOrder = { opps: [] };

                    allOpps.forEach((opp) => {
                        if (opp.salesPerson === user._id) {
                            if (opp?.orderId) {
                                opps.push(opp);
                            } else {
                                noOrder.opps.push(opp);
                            }
                        }
                    });

                    // Salesperson report object
                    const obj: any = {};
                    // obj.type = [];

                    obj.idx = idx;
                    obj.name = user.name; // `${user.profile?.firstName} ${user.profile?.lastName}`;
                    obj.salary =
                        wage?.wageInterval === WageIntervalEnum.Year // "Year"
                            ? roundTo2(wage.wageAmount / 24)
                            : wage?.wageInterval === WageIntervalEnum.Month
                            ? roundTo2(wage.wageAmount / 2)
                            : 0;

                    // attach 'noOrder's
                    obj.noOrder = noOrder;
                    obj.noOrder.num = noOrder.opps.length;

                    let otherTypeCommision = 0;

                    // for (const type of projectTypes) {
                    const sold = { opps: [], volume: 0, num: 0, commission: 0 };
                    const completed = { opps: [], volume: 0, num: 0, commission: 0 };
                    const started = { opps: [], volume: 0, num: 0, commission: 0 };

                    //for sold opp
                    sold.opps = opps
                        .filter((opp) => {
                            return (
                                new Date(opp.saleDate) >= reportStart && new Date(opp.saleDate) <= reportEnd
                                // && opp?.pType?._id === type._id
                            );
                        })
                        .map((opp) => {
                            // Create a deep copy of the opp object to resolve overwrite issue
                            const newOpp = JSON.parse(JSON.stringify(opp));
                            // const prices = newOpp?.order?.priceTotals;
                            newOpp.selfLead = newOpp.selfGen;
                            newOpp.commission = newOpp?.salesCommission?.sale;
                            newOpp.discountPercent = roundTo2(
                                100 * (newOpp?.discount / (newOpp?.soldValue + newOpp?.discount)),
                            );

                            return newOpp;
                        })
                        .sort((a, b) => {
                            const nameComparison = a.pType.name.localeCompare(b.pType.name);
                            if (nameComparison !== 0) {
                                return nameComparison;
                            }
                            return new Date(b.saleDate).getTime() - new Date(a.saleDate).getTime();
                        });

                    sold.num = sold.opps.length;
                    sold.volume = sumArray(sold.opps, "soldValue");
                    sold.commission = sumArray(sold.opps, "commission");
                    obj.sold = sold;

                    //for job started opp
                    started.opps = opps
                        .filter((opp) => {
                            return (
                                new Date(opp.jobStartedDate) >= reportStart &&
                                new Date(opp.jobStartedDate) <= reportEnd
                                // && opp?.pType?._id === type._id
                            );
                        })
                        .map((opp) => {
                            // Create a deep copy of the opp object to resolve overwrite issue
                            const newOpp = JSON.parse(JSON.stringify(opp));
                            newOpp.selfLead = newOpp.selfGen;
                            newOpp.commission = newOpp?.salesCommission?.start;
                            newOpp.discountPercent = roundTo2(
                                100 * (newOpp?.discount / (newOpp?.soldValue + newOpp?.discount)),
                            );
                            return newOpp;
                        })
                        .sort((a, b) => {
                            const nameComparison = a.pType.name.localeCompare(b.pType.name);
                            if (nameComparison !== 0) {
                                return nameComparison;
                            }
                            return (
                                new Date(b.jobStartedDate).getTime() - new Date(a.jobStartedDate).getTime()
                            );
                        });

                    started.num = started.opps.length;
                    started.volume = sumArray(started.opps, "soldValue");
                    started.commission = sumArray(started.opps, "commission");
                    obj.started = started;

                    //for job completed opp
                    completed.opps = opps
                        .filter((opp) => {
                            return (
                                new Date(opp.jobCompletedDate) >= reportStart &&
                                new Date(opp.jobCompletedDate) <= reportEnd
                                // && opp?.pType?._id === type._id
                            );
                        })
                        .map((opp) => {
                            // Create a deep copy of the opp object to resolve overwrite issue
                            const newOpp = JSON.parse(JSON.stringify(opp));
                            newOpp.selfLead = newOpp.selfGen;
                            newOpp.commission = newOpp?.salesCommission?.completed;
                            newOpp.discountPercent = roundTo2(
                                100 * (newOpp?.discount / (newOpp?.soldValue + newOpp?.discount)),
                            );
                            return newOpp;
                        })
                        .sort((a, b) => {
                            const nameComparison = a.pType.name.localeCompare(b.pType.name);
                            if (nameComparison !== 0) {
                                return nameComparison;
                            }
                            return (
                                new Date(b.jobCompletedDate).getTime() -
                                new Date(a.jobCompletedDate).getTime()
                            );
                        });

                    completed.num = completed.opps.length;
                    completed.volume = sumArray(completed.opps, "soldValue");
                    completed.commission = sumArray(completed.opps, "commission");
                    obj.completed = completed;

                    otherTypeCommision += completed.commission + sold.commission + started.commission;

                    // obj.type = {
                    //     // typeReplacement: true,
                    //     // name: type.name,
                    //     // _id: type._id,
                    // };
                    // }

                    // Roofs & Repairs & others Sold & Completed First Period
                    // const startMonthWage = findCurrentWage(user?.wage?.wageHistory, monthStart);
                    // const startMonthCommission = roundTo2(startMonthWage?.saleCommission / 100) ?? 0;

                    const firstPeriod: any = {};
                    firstPeriod.opps = opps
                        .filter((opp) => {
                            return (
                                new Date(opp.saleDate) >= monthStart && new Date(opp.saleDate) < reportStart
                            );
                        })
                        .map((opp) => {
                            // Create a deep copy of the opp object to resolve overwrite issue
                            const newOpp = JSON.parse(JSON.stringify(opp));
                            newOpp.selfLead = newOpp.selfGen;
                            newOpp.commission = newOpp?.salesCommission?.sale;
                            return newOpp;
                        })
                        .sort((a, b) => {
                            return new Date(b.saleDate).getTime() - new Date(a.saleDate).getTime();
                        });

                    firstPeriod.completed = opps
                        .filter((opp) => {
                            return (
                                new Date(opp.jobCompletedDate) >= new Date(monthStart) &&
                                new Date(opp.jobCompletedDate) < reportStart
                            );
                        })
                        .map((opp) => {
                            // Create a deep copy of the opp object to resolve overwrite issue
                            const newOpp = JSON.parse(JSON.stringify(opp));
                            newOpp.selfLead = newOpp.selfGen;
                            newOpp.commission = newOpp?.salesCommission?.completed;
                            return newOpp;
                        })
                        .sort((a, b) => {
                            return (
                                new Date(b.jobCompletedDate).getTime() -
                                new Date(a.jobCompletedDate).getTime()
                            );
                        });

                    // first period modified commission
                    const firstPeriodModComm =
                        oppModifiedComm
                            ?.filter(
                                (c) =>
                                    c.salesPersonId === user._id &&
                                    new Date(c.date) >= monthStart &&
                                    new Date(c.date) <= reportStart,
                            )
                            ?.reduce((sum, c) => sum + (c.amount || 0), 0) || 0;

                    firstPeriod.commission =
                        sumArray(firstPeriod.opps, "commission") +
                        sumArray(firstPeriod.completed, "commission") +
                        firstPeriodModComm;
                    firstPeriod.num = firstPeriod.opps.length;
                    firstPeriod.volume = sumArray(firstPeriod.opps, "soldValue");
                    obj.firstPeriod = firstPeriod;

                    // modified commission calculation
                    obj.modified = {};
                    obj.modified.opp =
                        oppModifiedComm.filter(
                            (c) =>
                                c.salesPersonId === user._id &&
                                new Date(c.date) >= reportStart &&
                                new Date(c.date) <= reportEnd,
                        ) || [];
                    obj.modified.num = obj.modified.opp.length;
                    obj.modified.amount = obj.modified.opp.reduce((sum, c) => sum + (c.amount || 0), 0);
                    // obj.modified.volume = sumArray(firstPeriod.opps, "soldValue");

                    obj.commission = roundTo2(otherTypeCommision + (obj?.modified?.amount || 0)); // modified commission added with earned commission

                    // Benchmark bonus calculations
                    obj.useBenchmarkBonus = useBenchmarkBonus;
                    const marks: any = {};
                    marks.num = 0;
                    marks.bonus = 0;
                    if (useBenchmarkBonus) {
                        // Roofs & Repairs & others Sold Entire Month
                        const month: any = {};
                        month.opps = opps
                            .filter((opp) => {
                                return (
                                    new Date(opp.saleDate) >= monthStart &&
                                    new Date(opp.saleDate) <= reportEnd
                                );
                            })
                            .sort((a, b) => {
                                return b.saleDate.getTime() - a.saleDate.getTime();
                            });
                        month.num = month.opps.length;
                        month.volume = sumArray(month.opps, "soldValue");
                        obj.month = month;

                        // benchmarks calc
                        let i = 0;
                        while (benchmarks[i].goal <= month.volume) {
                            marks.num++;
                            marks.bonus += benchmarks[i].bonus;
                            i++;
                        }
                        marks.next = roundTo2(benchmarks[i].goal - month.volume);
                        marks.text = marks.num === 1 ? "benchmark" : "benchmarks";
                        obj.benchmarks = marks;
                    }
                    obj.totalEarned = roundTo2(obj.commission + obj.salary);
                    obj.monthEnd = false;

                    // adding 2 day for timezone issue to check if its start month date or not
                    const dateCheck = new Date(reportStart);
                    const startMonthChk = new Date(dateCheck.setDate(dateCheck.getDate() + 2)).getDate();
                    if (startMonthChk > 15) {
                        obj.monthEnd = true;
                        // obj.commission += commission ? marks.bonus : 0;
                        obj.totalEarned += commission ? marks.bonus : 0;
                    }

                    obj.monthEarned = roundTo2(obj.totalEarned + firstPeriod.commission + obj.salary);
                    report.salesPeople.push(obj);
                }),
            );

            return new OkResponse({ report });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async teamOnPayroll(userId: string, companyId: string, paySchId: string, startDate: Date, endDate: Date) {
        try {
            const reportStart = new Date(startDate);
            const reportEnd = new Date(endDate);

            const variables = await this.companySettingModel.findOne({ companyId });
            const paySchedule = await this.payScheduleModel.findOne({ _id: paySchId, companyId });

            const team = await this.memberModel.aggregate([
                {
                    $match: {
                        company: companyId,
                        deleted: { $ne: true },
                        $and: [{ hireDate: { $exists: true } }, { hireDate: { $lte: reportEnd } }],
                    },
                },
                {
                    $lookup: {
                        from: "Compensation",
                        localField: "_id",
                        foreignField: "memberId",
                        as: "wage",
                    },
                },
                {
                    $unwind: {
                        path: "$wage",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $lookup: {
                        from: "User",
                        localField: "user",
                        foreignField: "_id",
                        as: "userData",
                    },
                },
                {
                    $unwind: {
                        path: "$userData",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $addFields: {
                        firstName: "$userData.firstName",
                        lastName: "$userData.lastName",
                        username: "$userData.username",
                        email: "$userData.email",
                    },
                },
                {
                    $sort: {
                        lastName: 1,
                    },
                },
                {
                    $project: {
                        userData: 0,
                    },
                },
            ]);

            const workerArray = [];
            for (const member of team) {
                // const memberReport = {};

                // find the most current wage info
                const wage = findCurrentWage(member?.wage?.wageHistory, reportStart);

                // If no wage info skip this user
                if (!wage) {
                    continue;
                }

                // If on this pay schedule
                if (wage && wage.payScheduleId === paySchId) {
                    const cards = await this.timesheetModel
                        .find({ memberId: member._id, deleted: { $ne: true } })
                        .sort({ timeIn: 1 });

                    const weeks = this.getWeekHours(reportStart, reportEnd, cards, variables);
                    const report = this.getReportHours(reportStart, reportEnd, weeks);
                    const netPay = this.getNetPay(report, wage, paySchedule);

                    report.estimated = roundTo2(netPay);
                    member.report = report;
                    workerArray.push(member);
                }
            }

            return new OkResponse({ workerArray });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    // Calculate earned pay from report
    getNetPay(hoursReport, wage, paySchedule) {
        let earned = 0;

        if (wage.wageInterval === WageIntervalEnum.Hour) {
            earned = hoursReport.reg * wage.wageAmount + hoursReport.ot * wage.wageAmount * 1.5;
        } else {
            const yearly =
                wage.wageInterval === WageIntervalEnum.Month ? wage.wageAmount * 12 : wage.wageAmount;
            if (paySchedule.period === PeriodEnum.EveryWeek) {
                earned = yearly / 52;
            } else if (paySchedule.period === PeriodEnum.EveryOtherWeek) {
                earned = yearly / 26;
            } else if (paySchedule.period === PeriodEnum.TwicePerMonth) {
                earned = yearly / 24;
            } else {
                earned = yearly / 12;
            }
        }

        return Number(earned.toFixed(2));
    }

    //get week start dates for calcing overtime (input date objects)
    getWeekStartDates(reportStart, reportEnd, variables) {
        const weekStartNum: WeekEnum = variables.weekStartDay;

        const firstWeekStartDate = getWeekStart(weekStartNum, reportStart);
        const weekStartDates = [firstWeekStartDate];
        let i = 0;
        while (weekStartDates[i] < reportEnd) {
            const date = new Date(firstWeekStartDate);
            date.setDate(date.getDate() + (i + 1) * 7);
            weekStartDates.push(date);
            i++;
        }

        return weekStartDates;
    }

    // add up all the hours in the week for cards
    getWeekHours(reportStart: Date, reportEnd: Date, cards: any[], variables) {
        const dates = this.getWeekStartDates(reportStart, reportEnd, variables);

        const weeks = [];
        for (const weekStart of dates) {
            const weekEnd = new Date(weekStart);

            weekEnd.setMilliseconds(weekEnd.getMilliseconds() + 604799999);

            const week: any = {};
            week.hrs = 0;
            week.ot = 0;
            week.cards = [];
            for (const card of cards) {
                if (new Date(card.timeIn) >= weekStart && new Date(card.timeIn) <= weekEnd) {
                    week.hrs += card.hrs;
                    card.reg = card.hrs;
                    card.ot = 0;
                    if (week.hrs > 40) {
                        if (week.hrs - 40 >= card.hrs) {
                            card.ot += card.hrs;
                        } else if (week.hrs - 40 < card.hrs) {
                            card.ot = roundTo2(week.hrs - 40);
                        }
                        card.reg -= roundTo2(card.ot);
                    }
                    week.ot += card.ot;
                    week.cards.push(card);
                }
            }
            week.hrs = roundTo2(week.hrs);
            week.ot = roundTo2(week.ot);
            weeks.push(week);
        }
        return weeks;
    }

    // Finds all hours using start/end dates plus modified cards with reg/ot assigned to them
    getReportHours(reportStart, reportEnd, weeks) {
        const report: any = {};
        report.hrs = 0;
        report.reg = 0;
        report.ot = 0;
        report.unapproved = 0;
        report.cards = [];
        for (const week of weeks) {
            for (const card of week.cards) {
                if (new Date(card.timeIn) >= reportStart && new Date(card.timeIn) <= reportEnd) {
                    if (card.status !== "Approved") report.unapproved++;
                    report.hrs += card.hrs;
                    report.reg += card.reg;
                    report.ot += card.ot;
                    report.cards.push(card);
                }
            }
        }
        report.hrs = roundTo2(report.hrs);
        report.reg = roundTo2(report.reg);
        report.ot = roundTo2(report.ot);
        return report;
    }

    async crewPayrollReport(userId: string, companyId: string, startDate: Date, endDate: Date) {
        try {
            const reportStart = new Date(startDate);
            const reportEnd = new Date(endDate);

            if (reportEnd <= reportStart) {
                throw new BadRequestException("End date must be greater than start date");
            }

            const [crews, pwSettingAllData, companySetting, allWorkTaskData] = await Promise.all([
                this.crewModel.aggregate([
                    {
                        $match: {
                            companyId,
                            startDate: { $lte: reportEnd },
                            $or: [{ endDate: { $exists: false } }, { endDate: { $gte: reportStart } }],
                            deleted: { $ne: true },
                            // retired: { $ne: true },
                        },
                    },
                    {
                        $lookup: {
                            from: "CrewMember",
                            pipeline: [
                                {
                                    $match: {
                                        startDate: { $lte: reportEnd },
                                        $or: [
                                            { removeDate: { $exists: false } },
                                            { removeDate: { $gte: reportStart } },
                                        ],
                                    },
                                },
                            ],
                            localField: "_id",
                            foreignField: "crewId",
                            as: "members",
                        },
                    },
                    {
                        $sort: { order: 1 },
                    },
                ]),
                this.pieceWorkSettingModel.find({
                    companyId: companyId,
                }),
                this.companySettingModel.findOne({ companyId }),

                this.workTaskModel.find({ companyId }),
            ]);

            const crewPayrollReport = {
                totalHours: 0,
                totalEarned: 0,
                defaultPOHours: 0,
                defaultPOEarned: 0,
                totalPOHours: 0,
                totalPOEarned: 0,
                defaultPoName: companySetting?.defaultPO || "Default",
                crewData: [],
            };
            const { weekStartDay } = companySetting;

            const startDayOfWeek = Object.values(WeekEnum).indexOf(
                WeekEnum[weekStartDay as keyof typeof WeekEnum],
            );

            const weeksList = this.getWeeksList(
                reportStart,
                reportEnd,
                startDayOfWeek,
                (startDayOfWeek + 6) % 7,
            );

            const startDateOfFirstWeek = weeksList[0].start;
            const endDateOfLastWeek = weeksList[weeksList.length - 1].end;

            for (const crew of crews) {
                const crewReport: any = {
                    crewId: crew._id,
                    crewName: crew.name,
                    totalCrewEarned: 0,
                    totalCrewHours: 0,
                    crewMembers: [],
                    companyP0Earned: 0,
                    companyP0Hours: 0,
                    projectP0Earned: 0,
                    projectP0Hours: 0,
                };

                const filtered = !crew.members
                    ? []
                    : crew.members.filter((member) => {
                          return activeCrewMember(member, reportStart, reportEnd);
                      });

                const crewLeadId = findCrewLeadId(crew, reportStart);

                const memberDayReports = [];
                for (const member of filtered) {
                    const memberReport = {
                        memberId: member.memberId,
                        memberName: member.memberName,
                        memberTotal: {
                            hours: 0,
                            earned: 0,
                            breakdown: {
                                hourly: {
                                    hours: 0,
                                    earned: 0,
                                    regular: { earned: 0, hours: 0 },
                                    overtime: { earned: 0, hours: 0 },
                                },
                                leadBonus: 0,
                                travel: 0,
                                pieceWorkEarned: 0,
                                pieceWork: [],
                            },
                        },
                        defaultPO: {
                            hours: 0,
                            timecards: [],
                        },
                        allTimeCards: [],
                        dayData: [],
                    };

                    let weeklyRegularHours = 0;

                    // let weeklyOverTimeHours = 0;
                    for (
                        let i = new Date(startDateOfFirstWeek);
                        i <= endDateOfLastWeek;
                        i.setDate(i.getDate() + 1)
                    ) {
                        const start = new Date(i);
                        const end = new Date(i);
                        end.setDate(end.getDate() + 1);
                        end.setMilliseconds(end.getMilliseconds() - 1);
                        const { memDayObj, memObj }: any = await this.crewService.memberDailyData(
                            companyId,
                            member.memberId,
                            start,
                            end,
                            crewLeadId,
                            pwSettingAllData,
                            allWorkTaskData,
                            companySetting,
                        );

                        const {
                            breakdown: { pieceWorkEarned, pieceWork, travel },
                        } = memDayObj;

                        // daily hourly data
                        const hoursDiff = Math.min(memObj.hours, 40 - weeklyRegularHours);
                        memDayObj.breakdown.hourly.regular = {
                            hours: hoursDiff,
                            earned: roundTo2(memObj.wage) * hoursDiff,
                        };
                        weeklyRegularHours += hoursDiff;

                        const overTimeHours = Math.max(0, memObj.hours - hoursDiff);
                        memDayObj.breakdown.hourly.overtime = {
                            hours: overTimeHours,
                            earned: roundTo2(memObj.wage * (overTimeHours ? 1.5 : 1) * overTimeHours),
                        };
                        // weeklyOverTimeHours += overTimeHours;

                        if (memObj.day === 6) {
                            weeklyRegularHours = 0;
                            // weeklyOverTimeHours = 0;
                        }

                        if (start >= reportStart && start <= reportEnd) {
                            memberReport.memberTotal.breakdown.hourly.regular.earned +=
                                memDayObj.breakdown.hourly.regular.earned;
                            memberReport.memberTotal.breakdown.hourly.regular.hours +=
                                memDayObj.breakdown.hourly.regular.hours;
                            memberReport.memberTotal.breakdown.hourly.overtime.earned +=
                                memDayObj.breakdown.hourly.overtime.earned;
                            memberReport.memberTotal.breakdown.hourly.overtime.hours +=
                                memDayObj.breakdown.hourly.overtime.hours;

                            memberReport.memberTotal.breakdown.hourly.earned +=
                                memDayObj.breakdown.hourly.regular.earned +
                                memDayObj.breakdown.hourly.overtime.earned;
                            memberReport.memberTotal.breakdown.hourly.hours +=
                                memDayObj.breakdown.hourly.regular.hours +
                                memDayObj.breakdown.hourly.overtime.hours;

                            memberReport.memberTotal.breakdown.pieceWorkEarned += pieceWorkEarned;
                            memberReport.memberTotal.breakdown.travel += travel;

                            memDayObj.breakdown.hourly.earned =
                                memDayObj.breakdown.hourly.regular.earned +
                                memDayObj.breakdown.hourly.overtime.earned;
                            memDayObj.breakdown.hourly.hours =
                                memDayObj.breakdown.hourly.regular.hours +
                                memDayObj.breakdown.hourly.overtime.hours;

                            pieceWork.forEach(({ pwName, earned, amount, unit }) => {
                                const existingPieceWork = memberReport.memberTotal.breakdown.pieceWork.find(
                                    (item) => item.pwName === pwName,
                                );
                                if (existingPieceWork) {
                                    existingPieceWork.earned += earned;
                                    existingPieceWork.amount += amount;
                                } else {
                                    memberReport.memberTotal.breakdown.pieceWork.push({
                                        pwName,
                                        earned,
                                        amount,
                                        unit,
                                    });
                                }
                            });

                            memberReport.memberTotal.hours +=
                                memDayObj.breakdown.hourly.regular.hours +
                                memDayObj.breakdown.hourly.overtime.hours;
                            memberReport.memberTotal.earned +=
                                memDayObj.breakdown.hourly.regular.earned +
                                memDayObj.breakdown.hourly.overtime.earned +
                                pieceWorkEarned +
                                travel;

                            memDayObj.hours = memDayObj.breakdown.hourly.hours;
                            memDayObj.earned = memDayObj.breakdown.hourly.earned + pieceWorkEarned + travel;
                            crewPayrollReport.totalEarned += travel;

                            const hourlyRate =
                                memDayObj.breakdown.hourly.earned / memDayObj.breakdown.hourly.hours || 0;
                            // PO total hours and earning calculations
                            memObj.projects.forEach((project) => {
                                const projectHours = project?.hours || 0;
                                const projectTotal = project?.pwTotal || 0;
                                const projectEarned = projectTotal + hourlyRate * projectHours;
                                const projectCards = project?.cards || [];

                                if (project?.oppId === "companyDefaultPO") {
                                    crewPayrollReport.defaultPOHours += projectHours;
                                    crewPayrollReport.defaultPOEarned += projectEarned;
                                    crewReport.companyP0Earned += projectEarned + travel;
                                    crewReport.companyP0Hours += projectHours;

                                    memDayObj.poHours = projectHours;
                                    memDayObj.defaultPoTimeCards.push(...projectCards);

                                    // Default PO data
                                    memberReport.defaultPO.hours += projectHours;
                                    memberReport.defaultPO.timecards.push(...projectCards);
                                } else {
                                    crewPayrollReport.totalPOHours += projectHours;
                                    crewPayrollReport.totalPOEarned += projectEarned;
                                    crewReport.projectP0Earned += projectEarned + travel;
                                    crewReport.projectP0Hours += projectHours;
                                }

                                memDayObj.allTimeCards.push(...projectCards);
                                memberReport.allTimeCards.push(...projectCards);

                                crewPayrollReport.totalHours += projectHours;
                                crewPayrollReport.totalEarned += projectEarned;
                            });

                            memberReport.dayData.push(memDayObj);
                            memberDayReports.push(memObj);
                        }
                    }

                    crewReport.totalCrewEarned += memberReport.memberTotal.earned;
                    crewReport.totalCrewHours += memberReport.memberTotal.hours;
                    crewReport.crewMembers.push(memberReport);
                }

                const memberReport = [];
                for (const memObj of memberDayReports) {
                    const date = memObj.date;
                    const index = memberReport.findIndex(
                        (entry) => entry.date.toString() === date.toString(),
                    );
                    if (index !== -1) {
                        memberReport[index].membersData.push(memObj);
                    } else {
                        memberReport.push({ date: date, membersData: [memObj] });
                    }
                }

                memberReport.forEach(({ date, membersData }) => {
                    membersData.map((member) => {
                        if (member.memberId === crewLeadId) member.crewLead = true;
                    });
                    const leadBonus = calcCrewLeadBonus(membersData);
                    const memberDetail = membersData.find(
                        (memberData) => memberData?.memberId === crewLeadId,
                    );
                    const leadMemberBonus = memberDetail?.dayOff
                        ? 0
                        : memberDetail?.cards?.length === 0
                        ? 0
                        : leadBonus;

                    crewReport.crewMembers.forEach((crewMember) => {
                        if (crewMember?.memberId === crewLeadId) {
                            crewMember.dayData.forEach((memberDayData) => {
                                if (memberDayData.date.toString() === date.toString()) {
                                    memberDayData.breakdown.leadBonus = leadMemberBonus;
                                    memberDayData.earned += leadMemberBonus;
                                }
                            });

                            crewMember.memberTotal.breakdown.leadBonus += leadMemberBonus;
                            crewMember.memberTotal.earned += leadMemberBonus;
                            crewMember.earned += leadMemberBonus;
                            crewPayrollReport.totalEarned += leadMemberBonus;
                            crewReport.totalCrewEarned += leadMemberBonus;
                            crewReport.projectP0Earned += leadMemberBonus;
                        }
                    });
                });

                crewPayrollReport.crewData.push(crewReport);
            }

            crewPayrollReport.defaultPOHours = roundTo2(crewPayrollReport.defaultPOHours);
            crewPayrollReport.defaultPOEarned = roundTo2(crewPayrollReport.defaultPOEarned);
            crewPayrollReport.totalPOHours = roundTo2(crewPayrollReport.totalPOHours);
            crewPayrollReport.totalPOEarned = roundTo2(crewPayrollReport.totalPOEarned);

            crewPayrollReport.totalHours = roundTo2(crewPayrollReport.totalHours);
            crewPayrollReport.totalEarned = roundTo2(crewPayrollReport.totalEarned);
            return new OkResponse({ crewPayrollReport });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    getWeeksList(
        startDate: Date,
        endDate: Date,
        startDayOfWeek: number,
        endDayOfWeek: number,
    ): { start: Date; end: Date }[] {
        const weeksList: { start: Date; end: Date }[] = [];
        const currentWeekStart = new Date(startDate);
        currentWeekStart.setDate(currentWeekStart.getDate() - currentWeekStart.getDay() + startDayOfWeek);

        while (currentWeekStart <= endDate) {
            const currentWeekEnd = new Date(currentWeekStart);
            currentWeekEnd.setDate(
                currentWeekEnd.getDate() + ((endDayOfWeek - currentWeekEnd.getDay() + 7) % 7),
            );

            // Set the time to 1 millisecond before midnight (23:59:59.999)
            currentWeekEnd.setHours(23, 59, 59, 999);

            weeksList.push({ start: new Date(currentWeekStart), end: new Date(currentWeekEnd) });

            // Move to the next week
            currentWeekStart.setDate(currentWeekStart.getDate() + 7);
        }

        return weeksList;
    }

    async payrollReport(
        logInMemberId: string,
        companyId: string,
        startDate: Date,
        endDate: Date,
        teamPermission: PermissionsEnum,
        memberIds: string,
    ) {
        try {
            const payrollReport = {
                members: [],
                totalHours: 0,
            };

            const reportStart = new Date(startDate);
            const reportEnd = new Date(endDate);

            const selectedMemberIds = memberIds.split(",");

            const members = await this.memberModel.find({
                _id: { $in: selectedMemberIds },
                company: companyId,
            });

            // Fetch all time cards for all members in one query
            const allTimeCards = await this.crewService.getTimeCardsForQuery({
                deleted: { $ne: true },
                memberId: { $in: selectedMemberIds },
                timeIn: { $gte: reportStart, $lte: reportEnd },
            });

            for (const member of members) {
                const memObj = {
                    name: member.name,
                    totalHours: 0,
                    dates: [],
                };

                const memberTimeCards = allTimeCards.filter((card) => card.memberId === member._id);

                for (let i = new Date(reportEnd); i > reportStart; i.setDate(i.getDate() - 1)) {
                    let dayOff = false;
                    let ptoUsed = false;
                    const startOfDay = new Date(i);
                    const endOfDay = new Date(i);
                    startOfDay.setDate(startOfDay.getDate() - 1);
                    startOfDay.setMilliseconds(startOfDay.getMilliseconds() + 1);

                    const hours = memberTimeCards.reduce((sum, card) => {
                        if (card.timeIn >= startOfDay && card.timeIn <= endOfDay) {
                            dayOff = card.task === TaskTypeEnum.Day_Off;
                            ptoUsed = card?.ptoUsed || false;
                            return sum + (card?.hrs || 0);
                        }
                        return sum;
                    }, 0);

                    memObj.totalHours += hours;
                    memObj.dates.push({
                        dayOff,
                        ptoUsed,
                        date: startOfDay,
                        hours: hours,
                        cards: memberTimeCards.filter((c) => c.timeIn >= startOfDay && c.timeIn <= endOfDay),
                    });
                }

                payrollReport.members.push(memObj);
                payrollReport.totalHours += memObj.totalHours;
            }

            return new OkResponse({ payrollReport });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async nonCrewPayrollReport(
        logInMemberId: string,
        companyId: string,
        startDate: Date,
        endDate: Date,
        teamPermission: PermissionsEnum,
        salesPersonId?: string,
    ) {
        try {
            const data = await this.crewService.getNonCrewsToApprove(
                logInMemberId,
                companyId,
                startDate,
                endDate,
                teamPermission,
            );
            const salesPersonArr = salesPersonId ? salesPersonId.split(",") : [];
            const nonCrewReport = {
                members: [],
                totalHours: 0,
            };

            const reportStart = new Date(startDate);
            const reportEnd = new Date(endDate);

            const membersList = salesPersonId
                ? data.data.nonCrewReport.members.filter((member) => salesPersonArr.includes(member.memberId))
                : data.data.nonCrewReport.members;

            for (const member of membersList) {
                const memObj = {
                    name: member.name,
                    totalHours: 0,
                    dates: [],
                };

                const filteredCards = member.cards;

                for (let i = new Date(reportEnd); i > reportStart; i.setDate(i.getDate() - 1)) {
                    let dayOff = false;
                    let ptoUsed = false;
                    const startOfDay = new Date(i);
                    const endOfDay = new Date(i);
                    // set 'startOfDay' to start of day
                    startOfDay.setDate(startOfDay.getDate() - 1);
                    startOfDay.setMilliseconds(startOfDay.getMilliseconds() + 1);

                    const hours = filteredCards.reduce((sum, card) => {
                        if (card.timeIn >= startOfDay && card.timeIn <= endOfDay) {
                            dayOff = card.task === TaskTypeEnum.Day_Off;
                            ptoUsed = card?.ptoUsed || false;
                            return sum + (card?.hrs || 0);
                        }
                        return sum;
                    }, 0);

                    memObj.totalHours += hours;
                    memObj.dates.push({
                        dayOff,
                        ptoUsed,
                        date: startOfDay,
                        hours: hours,
                        cards: filteredCards.filter((c) => c.timeIn >= startOfDay && c.timeIn <= endOfDay),
                    });
                }

                nonCrewReport.members.push(memObj);
                nonCrewReport.totalHours += memObj.totalHours;
            }

            return new OkResponse({ nonCrewReport });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    async weeklyProjectReport(
        userId: string,
        companyId: string,
        startDate: Date,
        endDate: Date,
        crewIdArr: string,
    ) {
        try {
            let crewIds = [];
            if (crewIdArr) {
                crewIds = crewIdArr.split(",");
            }

            const reportStart = new Date(startDate);
            const reportEnd = new Date(endDate);
            const oppQuery = {
                companyId,
                deleted: false,
                jobCompletedDate: { $gte: reportStart, $lte: reportEnd },
            };
            if (crewIds.length) {
                oppQuery["workingCrew.id"] = { $in: crewIds };
            }

            const [projectTypes, allOpps] = await Promise.all([
                this.projectTypeModel.find({
                    companyId,
                    deleted: false,
                }),
                this.opportunityModel.aggregate([
                    {
                        $match: oppQuery,
                    },
                    {
                        $set: {
                            oppType: {
                                $cond: {
                                    if: {
                                        $and: [
                                            { $gt: ["$saleDate", null] },
                                            { $gt: ["$acceptedType", null] },
                                        ],
                                    },
                                    then: "$acceptedType",
                                    else: "$oppType",
                                },
                            },
                        },
                    },
                    {
                        $lookup: {
                            from: "Order",
                            foreignField: "_id",
                            localField: "orderId",
                            as: "order",
                        },
                    },
                    {
                        $unwind: {
                            path: "$order",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Price",
                            foreignField: "_id",
                            localField: "order.projectPriceId",
                            as: "projectPrice",
                        },
                    },
                    {
                        $unwind: {
                            path: "$projectPrice",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Contact",
                            foreignField: "_id",
                            localField: "contactId",
                            as: "contact",
                        },
                    },
                    {
                        $unwind: {
                            path: "$contact",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $project: {
                            _id: 1,
                            companyId: 1,
                            oppType: 1,
                            contactId: 1,
                            "contact.fullName": 1,
                            "contact.businessName": 1,
                            "contact.isBusiness": 1,
                            PO: 1,
                            num: 1,
                            salesPerson: 1,
                            stage: 1,
                            orderId: 1,
                            jobCompletedDate: 1,
                            acceptedProjectId: 1,
                            acceptedType: 1,
                            "order.priceTotals": 1,
                            "order.actualTotals": 1,
                            "order.projects": 1,
                            projectPrice: 1,
                            changeOrders: 1,
                        },
                    },
                ]),
            ]);
            const oppIds = [...new Set(allOpps.map((opp) => opp?._id))];
            const timecardQuery = {
                projectId: {
                    $in: oppIds,
                },

                deleted: { $ne: true },
            };

            if (crewIds && crewIds.length) {
                timecardQuery["crewId"] = { $in: crewIds };
            }
            const timeCards = await this.crewService.getTimeCardsForQuery(timecardQuery);

            const completedOpps = [];

            let totalHrs = 0;
            // allOpps.forEach((opp: any) =>
            for (let i = 0; i < allOpps.length; i++) {
                const opp: any = allOpps[i];
                const order: any = opp?.order;
                if (!order || !opp.acceptedProjectId) return;

                const {
                    data: { extras },
                } = await this.dailyLogService.dailylogExtraWorkForOpp(companyId, opp._id);

                const price = order?.priceTotals;
                const projectPrice = opp?.projectPrice;
                const hourCost = projectPrice.variables?.manHourRate;
                const plyCost = projectPrice.variables?.plywoodRate;
                const matMarkup = projectPrice.variables?.matMarkup * 100;
                const ttlBurden = projectPrice.variables?.ttlBurden;

                opp.left = price?.jobTotal - price?.jobCost - price?.permit - price?.asbTest;
                const { projects } = order;
                const allWorkOrders = [];
                projects.forEach(({ workOrder }) => {
                    allWorkOrders.push(...workOrder);
                });
                const budget: any = {};

                // calculation for estimated hrs
                budget.ttlHours = allWorkOrders?.reduce((hrs, workOrder) => {
                    return hrs + (workOrder.ttlHours || 0);
                }, 0);
                // adding tavel time to estimate hrs
                budget.ttlHours += price?.travelFee / price?.travelHrlyRate || 0;
                // adding extra time for change order labor into estimate hrs (lSubtotal/ total hrs + travel hrs)
                const repairWorkTaskRate = price?.lSubtotal / budget.ttlHours || 0;
                budget.ttlHours +=
                    (opp?.changeOrders &&
                        opp?.changeOrders.reduce((hrs, changeOrder) => {
                            return hrs + (changeOrder?.labor / (repairWorkTaskRate * (1 + ttlBurden)) || 0);
                        }, 0)) ||
                    0;
                budget.ttlHours += extras.hours + extras.ply * 0.5;

                //NOTE: extraWork is not in use now in opp rmove it
                // Bill calculation to total
                const hours = opp.extraWork?.hours || 0;
                const ply = opp.extraWork?.ply || 0;
                const mats = opp.extraWork?.mats || 0;
                const totalHoursCost = roundTo2(hours * hourCost);
                const totalPlyCost = roundTo2(ply * 32 * plyCost);
                const totalMatCost = roundTo2(mats * (1 + matMarkup / 100));
                const billTotal = roundTo2(totalHoursCost + totalPlyCost + totalMatCost);

                const changeOrderTotal =
                    opp?.changeOrders?.reduce((total, cOrder) => total + (cOrder?.total || 0), 0) || 0;
                budget.total = price?.jobTotal + billTotal + changeOrderTotal + extras?.billTotal;

                // const salePrice = opp.soldValue + (opp.ttlExtraWork || 0);
                // const newTotal =
                //     salePrice + totalHoursCost + totalPlyCost + totalMatCost + (opp?.changeOrderValue || 0);

                const changeOrder =
                    opp?.changeOrders?.reduce((sum, item) => {
                        return sum + (item?.materials || 0);
                    }, 0) || 0;
                budget.materialBudget = budget.materialBudget || {};

                budget.materialBudget =
                    price.matCost +
                    price.matTax +
                    (price.permit || 0) +
                    (price.asbTest || 0) +
                    totalMatCost +
                    changeOrder;

                budget.matBreakdown = {
                    cost: price.matCost,
                    tax: price.matTax,
                    permit: price.permit || 0,
                    asbTest: price.asbTest || 0,
                    addMaterial: totalMatCost,
                    changeOrder: changeOrder,
                };

                const actual: any = {};
                actual.total =
                    order?.actualTotals?.actualPrice + billTotal + changeOrderTotal + extras?.billTotal;
                actual.ttlHours = timeCards.reduce((hrs, timecard) => {
                    if (timecard.projectId === opp._id) {
                        return hrs + timecard.hrs;
                    }
                    return hrs;
                }, 0);
                totalHrs += actual.ttlHours;

                opp.budget = budget;
                opp.actual = actual;

                completedOpps.push(opp);
            }

            const result = {
                totalHrs,
                num: completedOpps.length,
                types: [],
            };

            for (const p of projectTypes) {
                const obj = {
                    name: p.name,
                    opps: [],
                    num: 0,
                };

                obj.opps = completedOpps.filter((opp) => opp.acceptedType === p._id);
                obj.num = obj.opps.length;
                result.types.push(obj);
            }
            return new OkResponse({ result });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            }
            throw new InternalServerErrorException(error.message);
        }
    }

    /**
     * Generates a client value report based on completed jobs within a date range
     * @param companyId - The company ID
     * @param startDate - Start date for the report period
     * @param endDate - End date for the report period
     * @returns Client value report with metrics like gross profit, volume, and lead source performance
     */
    async clientValueReport(companyId: string, startDate: Date, endDate: Date) {
        try {
            // Validate input parameters
            if (!companyId) throw new BadRequestException("Company ID is required");
            if (!startDate || !endDate) throw new BadRequestException("Start and end dates are required");
            if (new Date(startDate) > new Date(endDate))
                throw new BadRequestException("Start date must be before end date");

            const reportStart = new Date(startDate);
            const reportEnd = new Date(endDate);

            // Early projection to reduce data transfer
            const [allOpps, allCampaigns] = await Promise.all([
                this.opportunityModel.aggregate([
                    {
                        $match: {
                            companyId,
                            deleted: false,
                            saleDate: { $gte: reportStart, $lte: reportEnd },
                            // jobCompletedDate: { $gte: reportStart, $lte: reportEnd },
                        },
                    },
                    {
                        $project: {
                            _id: 1,
                            companyId: 1,
                            oppType: 1,
                            contactId: 1,
                            PO: 1,
                            num: 1,
                            orderId: 1,
                            jobCompletedDate: 1,
                            acceptedProjectId: 1,
                            acceptedType: 1,
                            leadSourceId: 1,
                            campaignId: 1,
                            saleDate: 1,
                            changeOrders: 1,
                            stage: 1,
                            soldValue: 1,
                            changeOrderValue: 1,
                        },
                    },
                    {
                        $set: {
                            oppType: {
                                $cond: {
                                    if: {
                                        $and: [
                                            { $gt: ["$saleDate", null] },
                                            { $gt: ["$acceptedType", null] },
                                        ],
                                    },
                                    then: "$acceptedType",
                                    else: "$oppType",
                                },
                            },
                        },
                    },
                    {
                        $lookup: {
                            from: "Order",
                            foreignField: "_id",
                            localField: "orderId",
                            as: "order",
                            pipeline: [{ $project: { actualTotals: 1, projectPriceId: 1, priceTotals: 1 } }],
                        },
                    },
                    {
                        $unwind: {
                            path: "$order",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "CrmStage",
                            localField: "stage",
                            foreignField: "_id",
                            as: "stageData",
                            pipeline: [
                                {
                                    $project: {
                                        _id: 1,
                                        stageGroup: 1,
                                    },
                                },
                            ],
                        },
                    },
                    {
                        $unwind: {
                            path: "$stageData",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "ProjectType",
                            foreignField: "_id",
                            localField: "oppType",
                            as: "pType",
                            pipeline: [{ $project: { _id: 1, name: 1, typeReplacement: 1 } }],
                        },
                    },
                    {
                        $unwind: {
                            path: "$pType",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Price",
                            foreignField: "_id",
                            localField: "order.projectPriceId",
                            as: "projectPrice",
                            pipeline: [{ $project: { variables: 1 } }],
                        },
                    },
                    {
                        $unwind: {
                            path: "$projectPrice",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Contact",
                            foreignField: "_id",
                            localField: "contactId",
                            as: "contact",
                            pipeline: [{ $project: { fullName: 1, businessName: 1, isBusiness: 1 } }],
                        },
                    },
                    {
                        $unwind: {
                            path: "$contact",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "LeadSource",
                            foreignField: "_id",
                            localField: "leadSourceId",
                            as: "leadSourceData",
                            pipeline: [{ $project: { name: 1, cost: 1, actualCost: 1 } }],
                        },
                    },
                    {
                        $unwind: {
                            path: "$leadSourceData",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $lookup: {
                            from: "Campaign",
                            foreignField: "_id",
                            localField: "campaignId",
                            as: "campaignData",
                            pipeline: [{ $project: { name: 1, spend: 1 } }],
                        },
                    },
                    {
                        $unwind: {
                            path: "$campaignData",
                            preserveNullAndEmptyArrays: true,
                        },
                    },
                    {
                        $project: {
                            _id: 1,
                            companyId: 1,
                            oppType: 1,
                            stageGroup: "$stageData.stageGroup",
                            oppTypeName: "$pType.name",
                            contactId: 1,
                            stage: 1,
                            "contact.fullName": 1,
                            "contact.businessName": 1,
                            "contact.isBusiness": 1,
                            PO: 1,
                            num: 1,
                            orderId: 1,
                            jobCompletedDate: 1,
                            acceptedProjectId: 1,
                            // acceptedType: 1,
                            leadSourceId: 1,
                            leadSourceData: 1,
                            campaignId: 1,
                            campaignData: 1,
                            "order.priceTotals": 1,
                            "order.actualTotals": 1,
                            projectPrice: 1,
                            changeOrders: 1,
                            soldValue: 1,
                            changeOrderValue: 1,
                        },
                    },
                ]),
                this.campaignModel.find({
                    companyId,
                    deleted: false,
                }),
            ]);
            // Get commission modifications for all opportunities
            const oppComm = await this.commissionModificationModel.find({
                oppId: { $in: allOpps.map((o) => o._id) },
                companyId,
            });

            // Extract unique contacts
            const uniqueContacts = new Map();
            allOpps.forEach((o) => {
                if (o.contactId && !uniqueContacts.has(o.contactId)) {
                    uniqueContacts.set(o.contactId, {
                        _id: o.contactId,
                        fullName: o.contact?.fullName,
                        businessName: o.contact?.businessName,
                        isBusiness: o.contact?.isBusiness,
                    });
                }
            });
            const allContact = Array.from(uniqueContacts.values());

            // Initialize result object
            const result = {
                gpc: 0,
                ratio: 0,
                cac: 0,
                totalSpend: 0,
                grossProfit: 0,
                volume: 0,
                clientsCount: allContact.length,
                clients: [],
                leadSources: [],
            };

            // Process clients
            const processedClients = [];

            for (const contact of allContact) {
                const clientOpp = allOpps.filter((o) => o.contactId === contact._id);

                const clientData = {
                    ...contact,
                    grossProfit: 0,
                    volume: 0,
                    oppCount: clientOpp.length,
                    opps: [],
                };

                for (let i = 0; i < clientOpp.length; i++) {
                    const opp: any = clientOpp[i];
                    const order: any = opp?.order;

                    // Skip if order or acceptedProjectId is missing
                    if (!order || !opp.acceptedProjectId) continue;

                    opp.jobDone = this.jobDoneIndicator(order);
                    const oppId = opp._id;

                    const projectPrice = opp?.projectPrice;
                    if (!projectPrice?.variables) continue; // Skip if project price variables are missing

                    const price = order.priceTotals;
                    if (!price) continue; // Skip if price totals are missing

                    // Calculate modified commission
                    const modifiedCommission = roundTo2(
                        (price.commission || 0) +
                            oppComm
                                .filter((o) => o.oppId === opp._id)
                                .reduce((sum, item) => {
                                    return sum + (item?.amount || 0);
                                }, 0),
                    );

                    // Gross profit calculation
                    let actualPrice = 0;
                    const comm = modifiedCommission; // Actual commission paid

                    let grossProfit = 0;
                    if (opp.jobDone) {
                        const materials = order?.actualTotals?.actualMatCost ?? 0;
                        const laborCost = order?.actualTotals?.actualLaborCost ?? 0;
                        const subCost = order?.actualTotals?.subcontractorCost ?? 0;
                        const laborBurden = laborCost * (projectPrice.variables.ttlBurden || 0);
                        const labor = laborCost + laborBurden + subCost;
                        actualPrice = order?.actualTotals?.actualPrice ?? 0;
                        grossProfit = actualPrice - comm - materials - labor;
                    } else {
                        const [crewReport, logs] = await Promise.all([
                            this.crewProjectReport(companyId, oppId),
                            this.getDailylogFroQuery({
                                projects: {
                                    $elemMatch: {
                                        oppId,
                                    },
                                },
                                deleted: { $ne: true },
                            }),
                        ]);

                        const { allWorkOrders } = crewReport;

                        // logs
                        const logTotals = {
                            roofSq: 0,
                            tearSq: 0,
                            plywood: 0,
                            hours: 0,
                            matCosts: 0,
                        };

                        logs.map((log) => {
                            const p = log.projects.find((project) => {
                                return project.oppId === oppId;
                            });
                            if (!p) return;

                            logTotals.roofSq += roundTo2(p?.roofingDone || p?.roofingSQ || 0);
                            logTotals.tearSq += roundTo2(p?.tearOffDone || p?.tearOffSQ || 0);
                            logTotals.plywood += roundTo2(p?.plywoodReplaced || p?.instSheet || 0);
                            logTotals.hours += roundTo2(p?.manHours || p?.addManHours || 0);
                            logTotals.matCosts += roundTo2(p?.materialCosts || p?.addMaterials || 0);
                        });

                        const { newTotal, materialBudget, laborBudget } = this.calculateProjectVolume(
                            opp?.changeOrderValue,
                            opp.soldValue,
                            allWorkOrders,
                            price,
                            projectPrice.variables,
                            opp?.changeOrders,
                            order?.modifiedBudget,
                            logTotals,
                        );
                        grossProfit = newTotal - (materialBudget + laborBudget + (price?.commission || 0));
                        actualPrice = newTotal;
                    }

                    // Update client data
                    clientData.grossProfit += grossProfit;
                    clientData.volume += actualPrice;
                    clientData.opps.push({
                        _id: opp._id,
                        grossProfit,
                        volume: actualPrice,
                        PO: opp.PO,
                        num: opp.num,
                        jobCompletedDate: opp.jobCompletedDate,
                        oppType: opp.oppType,
                        oppTypeName: opp.oppTypeName,
                        stage: opp.stage,
                        stageGroup: opp.stageGroup,
                        jobDone: opp.jobDone,
                    });

                    // Update result totals
                    result.grossProfit += grossProfit;
                    result.volume += actualPrice;
                }

                // Only add client if they have opportunities
                if (clientData.opps.length > 0) {
                    processedClients.push(clientData);
                }
            }

            // Add processed clients to result
            result.clients = processedClients;

            // Extract unique lead sources
            const uniqueLeadSources = new Map();
            allOpps.forEach((o) => {
                if (o.leadSourceId && !uniqueLeadSources.has(o.leadSourceId)) {
                    uniqueLeadSources.set(o.leadSourceId, {
                        _id: o.leadSourceId,
                        name: o.leadSourceData?.name,
                        campaignId: o.campaignId,
                        actualCost: o.leadSourceData?.actualCost,
                    });
                }
            });
            const allLeadSource = Array.from(uniqueLeadSources.values());

            // Process lead sources
            result.totalSpend = 0;
            for (const leadSource of allLeadSource) {
                if (!leadSource._id) return; // Skip if lead source ID is missing

                const leadSourceOpp = allOpps.filter((o) => o.leadSourceId === leadSource._id);

                // Get lead source spend
                let leadSourceSpend =
                    getActualCostByDates(leadSource?.actualCost, reportStart, reportEnd) || 0;

                // Find all campaigns associated with this lead source
                const relatedCampaigns = allCampaigns.filter(
                    (campaign) =>
                        campaign.leadSourceId &&
                        campaign.leadSourceId.toString() === leadSource._id.toString(),
                );

                // Add campaign costs to lead source spend
                relatedCampaigns.forEach((campaign) => {
                    if (campaign.actualCost && Array.isArray(campaign.actualCost)) {
                        // If campaign has actualCost array, get costs for the date range
                        const campaignCost = getActualCostByDates(
                            campaign.actualCost,
                            reportStart,
                            reportEnd,
                        );
                        leadSourceSpend += campaignCost || 0;
                    } else if (campaign.cost && campaign.isMonthly) {
                        // For monthly campaigns without actual costs, estimate based on monthly cost
                        // Calculate number of months in the report period
                        const startMonth = reportStart.getMonth() + 1; // 1-12
                        const startYear = reportStart.getFullYear();
                        const endMonth = reportEnd.getMonth() + 1; // 1-12
                        const endYear = reportEnd.getFullYear();

                        // Only count if campaign started before or during the report period
                        if (
                            campaign.startYear < endYear ||
                            (campaign.startYear === endYear && campaign.startMonth <= endMonth)
                        ) {
                            // Calculate months overlap
                            const totalMonths = (endYear - startYear) * 12 + (endMonth - startMonth) + 1;
                            leadSourceSpend += campaign.cost * totalMonths;
                        }
                    }
                });

                // Update total spend for result
                result.totalSpend += leadSourceSpend;

                const leadResult = {
                    name: leadSource.name || "Unknown",
                    ratio: 0,
                    gpc: 0,
                    cac: 0,
                    grossProfit: 0,
                    spend: leadSourceSpend,
                    clientCount: 0,
                    clients: [],
                }; // Process clients for this lead source
                const leadSourceClients = new Map();

                for (const opp of leadSourceOpp) {
                    const order: any = opp?.order;
                    if (!order || !opp.acceptedProjectId || !opp.projectPrice?.variables) continue;

                    opp.jobDone = this.jobDoneIndicator(order);
                    const oppId = opp._id;
                    const projectPrice = opp.projectPrice;
                    const price = order.priceTotals;
                    if (!price) continue;

                    // Calculate modified commission
                    const modifiedCommission = roundTo2(
                        (price.commission || 0) +
                            oppComm
                                .filter((o) => o.oppId === opp._id)
                                .reduce((sum, item) => {
                                    return sum + (item?.amount || 0);
                                }, 0),
                    );

                    const comm = modifiedCommission;
                    let actualPrice = 0;

                    let grossProfit = 0;

                    if (opp.jobDone) {
                        // Gross profit calculation (jobDone = true)
                        const materials = order?.actualTotals?.actualMatCost ?? 0;
                        const laborCost = order?.actualTotals?.actualLaborCost ?? 0;
                        const subCost = order?.actualTotals?.subcontractorCost ?? 0;
                        const laborBurden = laborCost * (projectPrice.variables.ttlBurden || 0);
                        const labor = laborCost + laborBurden + subCost;
                        actualPrice = order?.actualTotals?.actualPrice ?? 0;
                        grossProfit = actualPrice - comm - materials - labor;
                    } else {
                        // Gross profit calculation (jobDone = false)
                        const [crewReport, logs] = await Promise.all([
                            this.crewProjectReport(companyId, oppId),
                            this.getDailylogFroQuery({
                                projects: {
                                    $elemMatch: {
                                        oppId,
                                    },
                                },
                                deleted: { $ne: true },
                            }),
                        ]);

                        const { allWorkOrders } = crewReport;

                        const logTotals = {
                            roofSq: 0,
                            tearSq: 0,
                            plywood: 0,
                            hours: 0,
                            matCosts: 0,
                        };

                        logs.map((log) => {
                            const p = log.projects.find((project) => project.oppId === oppId);
                            if (!p) return;

                            logTotals.roofSq += roundTo2(p?.roofingDone || p?.roofingSQ || 0);
                            logTotals.tearSq += roundTo2(p?.tearOffDone || p?.tearOffSQ || 0);
                            logTotals.plywood += roundTo2(p?.plywoodReplaced || p?.instSheet || 0);
                            logTotals.hours += roundTo2(p?.manHours || p?.addManHours || 0);
                            logTotals.matCosts += roundTo2(p?.materialCosts || p?.addMaterials || 0);
                        });
                        const { newTotal, materialBudget, laborBudget } = this.calculateProjectVolume(
                            opp?.changeOrderValue,
                            opp.soldValue,
                            allWorkOrders,
                            price,
                            projectPrice.variables,
                            opp?.changeOrders,
                            order?.modifiedBudget,
                            logTotals,
                        );

                        grossProfit = newTotal - (materialBudget + laborBudget + (price?.commission || 0));
                        actualPrice = newTotal;
                    }

                    // Update lead source gross profit
                    leadResult.grossProfit += grossProfit;

                    // Track unique clients
                    if (opp.contactId && !leadSourceClients.has(opp.contactId)) {
                        leadSourceClients.set(opp.contactId, {
                            _id: opp.contactId,
                            fullName: opp.contact?.fullName,
                            businessName: opp.contact?.businessName,
                            isBusiness: opp.contact?.isBusiness,
                            grossProfit,
                            volume: actualPrice,
                            oppCount: 1,
                            opps: [
                                {
                                    _id: opp._id,
                                    grossProfit,
                                    volume: actualPrice,
                                    PO: opp.PO,
                                    num: opp.num,
                                    jobCompletedDate: opp.jobCompletedDate,
                                    oppType: opp.oppType,
                                    oppTypeName: opp.oppTypeName,
                                    stage: opp.stage,
                                    stageGroup: opp.stageGroup,
                                    jobDone: opp.jobDone,
                                },
                            ],
                        });
                    } else if (opp.contactId) {
                        // Update existing client
                        const client = leadSourceClients.get(opp.contactId);
                        client.grossProfit += grossProfit;
                        client.volume += actualPrice;
                        client.oppCount += 1;
                        client.opps.push({
                            _id: opp._id,
                            grossProfit,
                            volume: actualPrice,
                            PO: opp.PO,
                            num: opp.num,
                            jobCompletedDate: opp.jobCompletedDate,
                            acceptedType: opp.acceptedType,
                            jobDone: opp.jobDone,
                        });
                    }
                }

                // Add clients to lead result
                leadResult.clients = Array.from(leadSourceClients.values());
                leadResult.clientCount = leadResult.clients.length;
                if (leadResult.clientCount > 0) {
                    leadResult.gpc = leadResult.grossProfit / leadResult.clientCount;
                    leadResult.cac = leadResult.spend / leadResult.clientCount;
                }

                // Calculate ratios (avoid division by zero)
                if (leadResult.spend > 0) {
                    leadResult.ratio = roundTo1(leadResult.grossProfit / leadResult.spend);
                }

                // Only add lead source if it has clients
                if (leadResult.clientCount > 0) {
                    result.leadSources.push(leadResult);
                }
            }

            // Calculate GPC and CAC for main result (avoid division by zero)
            if (result.clientsCount > 0) {
                result.gpc = result.grossProfit / result.clientsCount;
                result.cac = result.totalSpend / result.clientsCount;
            }

            // Calculate ratios for main result (avoid division by zero)
            if (result.totalSpend > 0) {
                result.ratio = roundTo1(result.grossProfit / result.totalSpend);
            }

            return new OkResponse({ result });
        } catch (error) {
            if (error instanceof HttpException) {
                throw error;
            } else if (error instanceof Error) {
                // Log the error for debugging
                console.error("Client Value Report Error:", error);
                throw new InternalServerErrorException(error.message);
            } else {
                throw new InternalServerErrorException("An unknown error occurred");
            }
        }
    }
}
