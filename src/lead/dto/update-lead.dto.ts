import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, IsString, IsUUID } from "class-validator";

export class UpdateLeadDto {
    @ApiPropertyOptional({ description: "Lead source Id" })
    @IsOptional()
    @IsUUID()
    leadSourceId: string;

    @ApiPropertyOptional({ description: "Campaign id" })
    @IsOptional()
    @IsUUID()
    campaignId: string;

    @ApiPropertyOptional({ description: "Referred Id" })
    @IsOptional()
    @IsUUID()
    referredBy: string;

    @ApiPropertyOptional({ description: "The type of work the lead is interested in" })
    @IsOptional()
    @IsString()
    workType?: string;

    @ApiPropertyOptional({ description: "New lead Date" })
    @IsOptional()
    newLeadDate: string;

    @ApiPropertyOptional({ description: "Tracking rule id" })
    @IsOptional()
    @IsUUID()
    trackingRuleId: string;
}
