import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsDate, IsNotEmpty, <PERSON>U<PERSON><PERSON> } from "class-validator";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Transform } from "class-transformer";

export class CreateLeadDto {
    // @ApiPropertyOptional({ description: "The email of the lead" })
    // @IsOptional()
    // @IsEmail()
    // @Transform(({ value }) => value.trim().toLowerCase(), { toClassOnly: true })
    // email: string;

    // @ApiProperty({ description: "The name of the lead" })
    // @IsString()
    // firstName: string;

    // @ApiPropertyOptional({ description: "The name of the lead" })
    // @IsOptional()
    // @IsString()
    // lastName: string;

    // @ApiPropertyOptional({ description: "The phone number of the lead" })
    // @IsOptional()
    // @IsString()
    // phone: string;

    @ApiPropertyOptional({ description: "stageId" })
    @IsUUID()
    @IsOptional()
    stageId: string;

    // @ApiPropertyOptional({ description: "Street address of the lead" })
    // @IsOptional()
    // @IsString()
    // street?: string;

    // @ApiPropertyOptional({ description: "State of the lead" })
    // @IsOptional()
    // @IsString()
    // state?: string;

    // @ApiPropertyOptional({ description: "City of the lead" })
    // @IsOptional()
    // @IsString()
    // city?: string;

    // @ApiPropertyOptional({ description: "Zip code of the lead" })
    // @IsOptional()
    // @IsString()
    // zip?: string;

    // @ApiPropertyOptional({ description: "Lead Source from where the lead was obtained" })
    // @IsString()
    // @IsOptional()
    // leadSource?: string;

    @ApiPropertyOptional({ description: "Campaign id" })
    @IsString()
    @IsOptional()
    campaignId?: string;

    @ApiPropertyOptional({ description: "Lead source Id" })
    @IsOptional()
    @IsUUID()
    leadSourceId?: string;

    @ApiPropertyOptional({ description: "Referred Id" })
    @IsOptional()
    @IsUUID()
    referredBy?: string;

    // @ApiPropertyOptional({ description: "Notes about the lead" })
    // @IsOptional()
    // @IsString()
    // notes?: string;

    // @ApiPropertyOptional({ description: "Actions taken for the lead" })
    // @IsOptional()
    // @IsString()
    // actions?: string;

    @ApiPropertyOptional({ description: "The type of work the lead is interested in" })
    @IsOptional()
    @IsString()
    workType?: string;

    @ApiProperty({ description: "lead date" })
    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsNotEmpty()
    newLeadDate: Date;

    @ApiPropertyOptional({ description: "Id of csr person" })
    @IsOptional()
    @IsUUID()
    csrId?: string;

    @ApiProperty({ description: "created by" })
    @IsNotEmpty()
    @IsUUID()
    createdBy: string;

    @ApiPropertyOptional({ description: "raw tracking data" })
    @IsOptional()
    rawTracking?: any;

    @ApiPropertyOptional({ description: "tracking data" })
    @IsOptional()
    tracking?: any[];
}
